# depconf/state.py
import os
import json
import logging
import time
from typing import Dict, Any, Optional, Set

logger = logging.getLogger('depconf.state')

REPO_STATE_FILENAME = "depconf_repo_scan_state.json"

def get_repo_state_path(output_dir: str) -> str:
    """Gets the full path for the repo scan state file."""
    os.makedirs(output_dir, exist_ok=True) # Ensure dir exists
    return os.path.join(output_dir, REPO_STATE_FILENAME)

def load_repo_scan_state(output_dir: str) -> Dict[str, Dict[str, Any]]:
    """Loads the repository scan state (last commit SHA and timestamp)."""
    state_file_path = get_repo_state_path(output_dir)
    repo_state: Dict[str, Dict[str, Any]] = {}
    if not os.path.exists(state_file_path):
        logger.info(f"Repo scan state file not found ('{state_file_path}'). Starting fresh.")
        return repo_state
    try:
        with open(state_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        if not isinstance(data, dict):
             logger.warning(f"Invalid format in state file {state_file_path}. Expected dictionary. Starting fresh.")
             return repo_state

        # Basic validation of inner structure
        valid_entries = 0
        for repo_name, state_data in data.items():
            if isinstance(state_data, dict) and \
               'last_commit_sha' in state_data and \
               'last_scan_timestamp' in state_data and \
               isinstance(state_data['last_commit_sha'], str) and \
               isinstance(state_data['last_scan_timestamp'], int):
                 repo_state[repo_name] = {
                     'last_commit_sha': state_data['last_commit_sha'],
                     'last_scan_timestamp': state_data['last_scan_timestamp']
                 }
                 valid_entries += 1
            else:
                 logger.warning(f"Skipping invalid state entry for repo '{repo_name}' in {state_file_path}")

        logger.info(f"📚 Loaded scan state for {valid_entries} repositories from {state_file_path}.")
    except json.JSONDecodeError:
        logger.warning(f"⚠️ Corrupt repo scan state file ('{state_file_path}'). Starting fresh.")
        return {}
    except Exception as e:
        logger.error(f"❌ Error loading repo scan state file: {e}", exc_info=True)
        return {}
    return repo_state

def save_repo_scan_state(output_dir: str, repo_state: Dict[str, Dict[str, Any]]) -> None:
    """Saves the repository scan state atomically using a temporary file.
    
    This approach ensures that the state file is only replaced if the write
    operation completes successfully, preventing corruption if the program
    crashes during the write.
    """
    state_file_path = get_repo_state_path(output_dir)
    temp_file_path = state_file_path + ".tmp"
    
    try:
        # Sort by repo name for consistent diffs
        sorted_repo_state = dict(sorted(repo_state.items()))
        
        # Write to temporary file first with proper error handling
        logger.debug(f"Writing state to temporary file: {temp_file_path}")
        try:
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                json.dump(sorted_repo_state, f, indent=2)
        except IOError as write_error:
            logger.error(f"Failed to write to temporary state file: {write_error}", exc_info=True)
            raise
            
        # Atomically replace the original file with the temporary file
        logger.debug(f"Atomically replacing state file: {state_file_path}")
        try:
            os.replace(temp_file_path, state_file_path)
        except OSError as replace_error:
            logger.error(f"Failed to atomically replace state file: {replace_error}", exc_info=True)
            # Clean up temp file since replace failed
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except Exception as cleanup_error:
                    logger.error(f"Failed to clean up temporary file after replace error: {cleanup_error}", exc_info=True)
            raise
        
        logger.info(f"💾 Saved scan state for {len(repo_state)} repositories to {state_file_path}")
    except Exception as e:
        logger.error(f"💥 Unexpected error saving repo scan state: {e}", exc_info=True)
        # Clean up temporary file if it exists
        if os.path.exists(temp_file_path):
            try:
                logger.debug(f"Cleaning up temporary state file: {temp_file_path}")
                os.remove(temp_file_path)
            except Exception as cleanup_error:
                logger.error(f"Failed to clean up temporary state file: {cleanup_error}", exc_info=True)
        raise  # Re-raise the original exception after cleanup

def update_and_save_repo_state_immediately(
    output_dir: str,
    repo_full_name: str,
    current_commit_sha: str,
    scan_timestamp: int,
    current_state: Dict[str, Dict[str, Any]]
) -> None:
    """Updates the state for a single repository and saves the entire state immediately.
    
    This function uses a retry mechanism to handle potential file system issues
    during the save operation.
    
    Args:
        output_dir: Directory where state file will be saved
        repo_full_name: Full name of the repository (e.g. 'owner/repo')
        current_commit_sha: Current commit SHA of the repository
        scan_timestamp: Unix timestamp of the scan (must be an integer)
        current_state: Current state dictionary to update
    """
    if not repo_full_name or not current_commit_sha:
        logger.warning("Attempted to update repo state with invalid name or SHA.")
        return
        
    if not isinstance(scan_timestamp, int):
        logger.warning(f"Invalid scan timestamp type for {repo_full_name}. Expected int, got {type(scan_timestamp)}.")
        return

    logger.debug(f"Updating state for {repo_full_name} with commit {current_commit_sha[:7]} and saving.")
    
    # Update the state
    current_state[repo_full_name] = {
        'last_commit_sha': current_commit_sha,
        'last_scan_timestamp': scan_timestamp
    }
    
    # Try to save with retries
    max_retries = 3
    retry_delay = 1  # seconds
    
    for attempt in range(max_retries):
        try:
            save_repo_scan_state(output_dir, current_state)
            return  # Success, exit function
        except Exception as e:
            if attempt < max_retries - 1:
                logger.warning(f"Failed to save state (attempt {attempt + 1}/{max_retries}): {e}. Retrying in {retry_delay}s...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                logger.error(f"Failed to save state after {max_retries} attempts: {e}", exc_info=True)
                raise  # Re-raise the last exception if all retries failed