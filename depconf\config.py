# depconf/config.py
import os
import yaml
import logging
import copy
# import shutil # Keep for shutil.which if validating other tools later? Or remove. Let's remove for now.
import re
from typing import Dict, Any, Optional, List

logger = logging.getLogger('depconf.config')

class ConfigManager:
    """Configuration manager for depconf."""

    DEFAULT_CONFIG = {
        'general': {
            'log_level': 'info',
            'output_dir': './depconf_results',
        },
        'github': { 'token': '', 'tokens': [], 'api_url': 'https://api.github.com' },
        'gitlab': { 'enabled': False, 'token': '', 'api_url': '', },
        'dependency_confusion': {
            'enabled': True,
            'secure_namespaces': [],
            'http_timeout': 20,
            'http_retries': 2,
            'proxies': [],  # List of proxy URLs to use for HTTP requests
            'proxy_rotation': False,  # Whether to rotate through proxies
            'proxy_rotation_factor': 5,  # Number of requests before rotating proxy
            'exclude_packages': [],       # General exclusions (applied to all ecosystems)
            'exclude_packages_pip': [],    # Specific pip ecosystem exclusions
            'exclude_packages_npm': [],    # Specific npm ecosystem exclusions
            'exclude_packages_rubygems': [], # Ruby/Gemfile exclusions
            'exclude_packages_maven': [],  # Java/Maven exclusions
            'exclude_packages_composer': [], # PHP/Composer exclusions
            'exclude_packages_gomodules': [], # Go modules exclusions
            'exclude_packages_nuget': [],  # .NET/NuGet exclusions
            'internal_package_pattern': '',
            
            # New additions from checker.py
            'top_packages': {
                "pip": [
                    "requests", "numpy", "pandas", "matplotlib", "django", "flask", "tensorflow", 
                    "scikit-learn", "scipy", "pytest", "beautifulsoup4", "pillow", "selenium", 
                    "sqlalchemy", "virtualenv", "boto3", "psycopg2", "pyyaml", "urllib3", "cryptography"
                ],
                "npm": [
                    "react", "express", "lodash", "moment", "chalk", "axios", "next", "typescript", 
                    "vue", "jquery", "webpack", "angular", "dotenv", "redux", "eslint", "jest", "babel", 
                    "node-fetch", "commander", "uuid"
                ],
                "rubygems": [
                    "rails", "rack", "json", "rspec", "rake", "nokogiri", "puma", "bundler", 
                    "activesupport", "redis", "devise", "sidekiq", "rubocop", "faker", "webpacker"
                ],
                "maven": [
                    "org.springframework:spring-core", "com.google.guava:guava", "org.slf4j:slf4j-api", 
                    "org.apache.commons:commons-lang3", "com.fasterxml.jackson.core:jackson-databind", 
                    "org.junit.jupiter:junit-jupiter-api", "org.mockito:mockito-core"
                ],
                "composer": [
                    "laravel/framework", "symfony/symfony", "phpunit/phpunit", "guzzlehttp/guzzle", 
                    "monolog/monolog", "doctrine/orm", "league/flysystem", "symfony/console"
                ],
                "gomodules": [
                    "github.com/gorilla/mux", "github.com/spf13/cobra", "github.com/gin-gonic/gin", 
                    "github.com/stretchr/testify", "github.com/prometheus/client_golang"
                ],
                "nuget": [
                    "Newtonsoft.Json", "Microsoft.AspNetCore", "Microsoft.Extensions.Logging", 
                    "System.Text.Json", "Serilog", "AutoMapper", "Dapper", "Microsoft.EntityFrameworkCore"
                ]
            },
            'registry_urls': {
                "pip": "https://pypi.org/pypi/{package}/json",
                "npm": "https://registry.npmjs.org/{package}",
                "rubygems": "https://rubygems.org/api/v1/gems/{package}.json",
                "maven": "https://repo1.maven.org/maven2/{group}/{artifact}/maven-metadata.xml",
                "composer": "https://packagist.org/packages/{package}.json",
                "gomodules": "https://proxy.golang.org/{package}/@v/list",
                "nuget": "https://api.nuget.org/v3/registration5-semver1/{package}/index.json"
            },
            'supported_files': {
                "requirements.txt": "pip",
                "requirements.in": "pip",
                "requirements-dev.txt": "pip",
                "requirements-dev.in": "pip",
                "pyproject.toml": "pip",
                "Pipfile": "pip",
                "Pipfile.lock": "pip",
                "poetry.lock": "pip",
                "package.json": "npm",
                "Gemfile": "rubygems",
                "pom.xml": "maven",
                "composer.json": "composer",
                "go.mod": "gomodules",
                "packages.config": "nuget"
            },
            'excluded_dirs': [
                '.git', 'node_modules', 'vendor', 'bower_components',
                'build', 'dist', 'target', '.svn', '.hg', '.gradle',
                '.venv', 'venv', 'env', '__pycache__', 'site-packages',
                'Pods', '.terraform', '.serverless', 'backup', 'tmp', 'temp',
                'testdata', 'fixtures', 'docs', 'examples', 'samples',
                '.idea', '.vscode', '.settings'
            ],
            'enable_typosquatting_detection': True,
            'typosquatting_threshold': 2,
            'typosquatting_length_divisor': 4,
            'enable_async': True,
            'async_ssl_verify': True,
            'max_concurrent_requests': 10,
            'connection_limit': 100
        },
        'operation': {
             'concurrency': 2,
             'shallow_clone_timeout': 300,
             'scan_interval': 3600,
             # List of patterns to watch for changes - only include actually supported files
             'package_file_patterns': [
                'package.json',                 # npm
                'requirements.txt',             # pip
                'requirements.in',              # pip
                'requirements-dev.txt',         # pip
                'requirements-dev.in',          # pip
                'pyproject.toml',               # pip
                'Gemfile',                      # rubygems
                'pom.xml',                      # maven
                'composer.json',                # composer/packagist
                'go.mod',                       # go modules
                '*.csproj',                     # nuget
                'packages.config'               # nuget
             ],
        },
        'notifications': {
            'telegram': { 'enabled': False, 'bot_token': '', 'chat_id': '' },
            'discord': { 'enabled': False, 'webhook_url': '' },
            'slack': {
                'enabled': False,
                'webhook_url': '',
                'channel': ''
            },
            'teams': {
                'enabled': False,
                'webhook_url': ''
            },
            'email': {
                'enabled': False,
                'smtp_server': '',
                'smtp_port': 587,
                'username': '',
                'password': '',
                'from_addr': '',
                'to_addrs': []
            }
        },
        'organizations': []
    }

    def __init__(self, config_path: Optional[str] = None):
        """Initialize the ConfigManager.
        
        Configuration precedence (from lowest to highest priority):
        1. Default values (DEFAULT_CONFIG)
        2. Environment variables (applied via _apply_env_vars)
        3. Config file values (applied via load_config_from_file)
        
        Args:
            config_path: Path to the configuration file (optional)
        """
        self.config_path = config_path
        self.config = copy.deepcopy(self.DEFAULT_CONFIG)
        self._apply_env_vars() 

        if config_path and os.path.exists(config_path):
            self.load_config_from_file(config_path) 
        elif config_path:
             logger.error(f"❌ Config file not found at specified path: {config_path}. Using defaults and environment variables.")
        else:
            logger.info("No config file path provided. Using defaults and environment variables.")

        self.validate_config() 

    def _apply_env_vars(self):
        logger.debug("Applying environment variable overrides...")
        # Handle GitHub tokens from environment variables
        if gh_token := os.getenv('GITHUB_TOKEN'):
            self.config['github']['token'] = gh_token
            logger.info("Applied GITHUB_TOKEN to single token config.")
            
            # Also add to tokens list if not already there
            if gh_token not in self.config['github']['tokens']:
                self.config['github']['tokens'].append(gh_token)
                logger.info("Added GITHUB_TOKEN to tokens list.")
                
        # Check for multiple GitHub tokens (GITHUB_TOKEN1, GITHUB_TOKEN2, etc.)
        token_index = 1
        while True:
            env_var_name = f'GITHUB_TOKEN{token_index}'
            if token := os.getenv(env_var_name):
                if token not in self.config['github']['tokens']:
                    self.config['github']['tokens'].append(token)
                    logger.info(f"Added {env_var_name} to tokens list.")
                token_index += 1
            else:
                break  # No more numbered tokens found
                
        # Handle other environment variables
        if gl_token := os.getenv('GITLAB_TOKEN'): self.config['gitlab']['token'] = gl_token; logger.info("Applied GITLAB_TOKEN.")
        if gl_url := os.getenv('GITLAB_URL'): self.config['gitlab']['api_url'] = gl_url; logger.info("Applied GITLAB_URL.")
        if tg_token := os.getenv('TELEGRAM_BOT_TOKEN'): self.config['notifications']['telegram']['bot_token'] = tg_token; logger.info("Applied TELEGRAM_BOT_TOKEN.")
        if tg_chat := os.getenv('TELEGRAM_CHAT_ID'): self.config['notifications']['telegram']['chat_id'] = tg_chat; logger.info("Applied TELEGRAM_CHAT_ID.")
        if dc_webhook := os.getenv('DISCORD_WEBHOOK_URL'): self.config['notifications']['discord']['webhook_url'] = dc_webhook; logger.info("Applied DISCORD_WEBHOOK_URL.")

    def load_config_from_file(self, config_path: str):
        logger.info(f"Attempting to load config from file: {config_path}")
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                loaded_config = yaml.safe_load(f)
            if not loaded_config or not isinstance(loaded_config, dict):
                logger.warning(f"Config file '{config_path}' empty/invalid. Ignoring.")
                return
            logger.info(f"✅ Loaded config from: {config_path}. Merging.")
            
            # Allow either:
            #   github:
            #     tokens: [ ... ]
            # or a legacy top-level:
            #   github_tokens: [ ... ]
            if tokens := loaded_config.pop('github_tokens', None):
                if isinstance(tokens, list):
                    loaded_config.setdefault('github', {})['tokens'] = tokens
                    logger.info(f"Added {len(tokens)} tokens from top-level 'github_tokens' to github.tokens")

            self._merge_configs(self.config, loaded_config)
        except FileNotFoundError: logger.error(f"❌ Config file not found: {config_path}.")
        except yaml.YAMLError as e: logger.error(f"❌ YAML parse error in '{config_path}': {e}. Ignored.")
        except Exception as e: logger.error(f"💥 Error loading config file '{config_path}': {e}.", exc_info=True)

    def _merge_configs(self, base_config: Dict, new_config: Dict) -> None:
        for key, value in new_config.items():
            if key in base_config:
                if isinstance(base_config[key], dict) and isinstance(value, dict): self._merge_configs(base_config[key], value)
                elif value is not None: base_config[key] = value
            elif value is not None: base_config[key] = value

    def validate_config(self) -> None:
        """Validates the final merged configuration."""
        logger.debug("Validating final configuration...")

        # Validate general section
        log_level = self.config.get('general', {}).get('log_level', 'info').upper()
        if log_level not in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            logger.warning(f"Invalid log_level '{log_level}'. Defaulting to INFO.")
            self.config['general']['log_level'] = 'info'
            
        if not self.config.get('general', {}).get('output_dir'):
            logger.error("❌ 'general.output_dir' missing. Defaulting to './depconf_results'.")
            self.config['general']['output_dir'] = './depconf_results'

        # Validate organizations
        orgs = self.config.get('organizations')
        if orgs is None:
            logger.warning("⚠️ 'organizations' key missing.")
            self.config['organizations'] = []
        elif not isinstance(orgs, list):
            logger.error("❌ 'organizations' must be a list.")
            self.config['organizations'] = []

        # Validate GitHub configuration
        gh_cfg = self.config.get('github', {})
        if not gh_cfg.get('tokens') and gh_cfg.get('token') and self.config.get('organizations'):
            logger.warning("⚠️ GitHub single token (`github.token`) is set, but `github.tokens` list is empty. Consider migrating to only use the `tokens` list. Private repo access fails & rate limits apply if the list remains empty and the single token is also removed from env.")
        elif not gh_cfg.get('tokens') and not gh_cfg.get('token') and self.config.get('organizations'):
            logger.warning("⚠️ GitHub tokens list is empty and no single token is set. Private repo access fails & rate limits apply.")
        elif not gh_cfg.get('tokens') and self.config.get('organizations'): # This case implies GITHUB_TOKEN env var might have populated the singular 'token' but not 'tokens' list if it was empty
             logger.warning("⚠️ GitHub tokens list is empty (potentially relying on a single GITHUB_TOKEN env var not populated into the list if it was initially empty). Private repo access may fail or be rate-limited if the list isn't correctly populated.")

        # Validate GitLab configuration
        gitlab_cfg = self.config.get('gitlab', {})
        if gitlab_cfg.get('enabled'):
            if not gitlab_cfg.get('api_url'):
                logger.error("❌ GitLab enabled, but 'api_url' missing. Disabling.")
                self.config['gitlab']['enabled'] = False
            elif not gitlab_cfg.get('token') and self.config.get('organizations'):
                logger.warning("⚠️ GitLab enabled, but token missing. Private access fails.")

        # Get dependency confusion config section
        dep_conf_cfg = self.config.get('dependency_confusion', {})
        
        # Validate HTTP settings
        http_timeout = dep_conf_cfg.get('http_timeout', 20)
        if not isinstance(http_timeout, (int, float)) or http_timeout <= 0:
            logger.warning(f"Invalid 'dependency_confusion.http_timeout'. Defaulting to 20s.")
            dep_conf_cfg['http_timeout'] = 20

        http_retries = dep_conf_cfg.get('http_retries', 2)
        if not isinstance(http_retries, int) or not (0 <= http_retries <= 5):
            logger.warning(f"Invalid 'dependency_confusion.http_retries' (must be int 0-5). Defaulting to 2.")
            dep_conf_cfg['http_retries'] = 2

        # Validate internal package pattern
        pattern = dep_conf_cfg.get('internal_package_pattern', '')
        if pattern:
            try:
                re.compile(pattern)
                logger.info(f"ℹ️ Using internal package pattern regex: {pattern}")
            except re.error as e:
                logger.error(f"❌ Invalid regex for 'internal_package_pattern': {e}. Pattern ignored.")
                dep_conf_cfg['internal_package_pattern'] = ''

        # Validate list-type configuration keys
        for key in ['secure_namespaces', 'proxies', 'exclude_packages', 'exclude_packages_pip', 'exclude_packages_npm',
                   'exclude_packages_rubygems', 'exclude_packages_maven', 'exclude_packages_composer',
                   'exclude_packages_gomodules', 'exclude_packages_nuget']:
            value = dep_conf_cfg.get(key, [])
            if not isinstance(value, list):
                logger.error(f"❌ Config error: 'dependency_confusion.{key}' must be list.")
                dep_conf_cfg[key] = []
            elif not all(isinstance(item, str) for item in value):
                logger.warning(f"⚠️ Config warning: all items in 'dependency_confusion.{key}' should be strings.")
                dep_conf_cfg[key] = [str(item) for item in value if item is not None]
                logger.info(f"ℹ️ Filtered {key}: {', '.join(dep_conf_cfg[key])}")
            elif value:
                logger.info(f"ℹ️ Using {key}: {', '.join(map(str, value))}")

        # Validate proxy configuration
        proxy_rotation = dep_conf_cfg.get('proxy_rotation', False)
        if not isinstance(proxy_rotation, bool):
            logger.warning(f"Invalid 'dependency_confusion.proxy_rotation' (must be boolean). Defaulting to False.")
            dep_conf_cfg['proxy_rotation'] = False
            
        proxy_rotation_factor = dep_conf_cfg.get('proxy_rotation_factor', 5)
        if not isinstance(proxy_rotation_factor, int) or proxy_rotation_factor <= 0:
            logger.warning(f"Invalid 'dependency_confusion.proxy_rotation_factor' (must be positive int). Defaulting to 5.")
            dep_conf_cfg['proxy_rotation_factor'] = 5

        # Validate proxies
        dep_conf_cfg['proxies'] = self._validate_proxies(dep_conf_cfg.get('proxies', []))

        # Validate operation settings
        op_cfg = self.config.get('operation', {})
        for key, default_val in [
            ('concurrency', 2),
            ('shallow_clone_timeout', 300),
            ('scan_interval', 3600),
        ]:
            val = op_cfg.get(key, default_val)
            if not isinstance(val, int) or val <= 0:
                logger.warning(f"Invalid 'operation.{key}'. Defaulting to {default_val}.")
                op_cfg[key] = default_val

        # Validate package_file_patterns
        patterns = op_cfg.get('package_file_patterns', [])
        if not isinstance(patterns, list) or not all(isinstance(p, str) for p in patterns):
            logger.warning("Invalid 'operation.package_file_patterns'; defaulting to empty list.")
            op_cfg['package_file_patterns'] = []

        # Validate notification settings
        notifications_cfg = self.config.get('notifications', {})
        
        # Validate Telegram settings
        telegram_cfg = notifications_cfg.get('telegram', {})
        if telegram_cfg.get('enabled'):
            if not telegram_cfg.get('bot_token') or not telegram_cfg.get('chat_id'):
                logger.error("❌ Telegram enabled but token/chat_id missing!")
                telegram_cfg['enabled'] = False

        # Validate Discord settings
        discord_cfg = notifications_cfg.get('discord', {})
        if discord_cfg.get('enabled') and not discord_cfg.get('webhook_url'):
            logger.error("❌ Discord enabled but webhook_url missing!")
            discord_cfg['enabled'] = False

        # Validate Slack settings
        slack_cfg = notifications_cfg.get('slack', {})
        if slack_cfg.get('enabled'):
            if not slack_cfg.get('webhook_url'):
                logger.error("❌ Slack enabled but webhook_url missing!")
                slack_cfg['enabled'] = False
            elif not slack_cfg.get('channel'):
                logger.warning("⚠️ Slack enabled but channel not specified. Notifications may not be delivered to the intended channel.")

        # Validate Teams settings
        teams_cfg = notifications_cfg.get('teams', {})
        if teams_cfg.get('enabled') and not teams_cfg.get('webhook_url'):
            logger.error("❌ Teams enabled but webhook_url missing!")
            teams_cfg['enabled'] = False

        # Validate Email settings
        email_cfg = notifications_cfg.get('email', {})
        if email_cfg.get('enabled'):
            if not email_cfg.get('smtp_server'):
                logger.error("❌ Email enabled but smtp_server missing!")
                email_cfg['enabled'] = False
            elif not email_cfg.get('from_addr'):
                logger.error("❌ Email enabled but from_addr missing!")
                email_cfg['enabled'] = False
            elif not email_cfg.get('to_addrs'):
                logger.error("❌ Email enabled but to_addrs missing!")
                email_cfg['enabled'] = False
            else:
                # Validate email addresses
                email_cfg['to_addrs'] = self._validate_email_addresses(email_cfg['to_addrs'])
                if not email_cfg['to_addrs']:
                    logger.error("❌ No valid email addresses in to_addrs!")
                    email_cfg['enabled'] = False

            # Validate SMTP port
            smtp_port = email_cfg.get('smtp_port', 587)
            if not isinstance(smtp_port, int) or not (1 <= smtp_port <= 65535):
                logger.warning(f"Invalid SMTP port {smtp_port}. Defaulting to 587.")
                email_cfg['smtp_port'] = 587

        # Validate dependency confusion settings
        dep_conf_cfg['top_packages'] = self._validate_top_packages(dep_conf_cfg.get('top_packages'))
        dep_conf_cfg['registry_urls'] = self._validate_registry_urls(dep_conf_cfg.get('registry_urls'))
        dep_conf_cfg['supported_files'] = self._validate_supported_files(dep_conf_cfg.get('supported_files'))
        dep_conf_cfg['excluded_dirs'] = self._validate_excluded_dirs(dep_conf_cfg.get('excluded_dirs'))
        
        # Validate typosquatting settings
        dep_conf_cfg['enable_typosquatting_detection'] = self._validate_boolean(
            dep_conf_cfg.get('enable_typosquatting_detection'),
            'enable_typosquatting_detection',
            True
        )
        dep_conf_cfg['typosquatting_threshold'] = self._validate_integer_range(
            dep_conf_cfg.get('typosquatting_threshold'),
            'typosquatting_threshold',
            2,
            1,
            5
        )
        dep_conf_cfg['typosquatting_length_divisor'] = self._validate_integer_range(
            dep_conf_cfg.get('typosquatting_length_divisor'),
            'typosquatting_length_divisor',
            4,
            1,
            10
        )

        # Validate async settings
        dep_conf_cfg['enable_async'] = self._validate_boolean(
            dep_conf_cfg.get('enable_async'),
            'enable_async',
            True
        )
        dep_conf_cfg['async_ssl_verify'] = self._validate_boolean(
            dep_conf_cfg.get('async_ssl_verify'),
            'async_ssl_verify',
            True
        )
        dep_conf_cfg['max_concurrent_requests'] = self._validate_integer_range(
            dep_conf_cfg.get('max_concurrent_requests'),
            'max_concurrent_requests',
            10,
            1,
            20
        )
        dep_conf_cfg['connection_limit'] = self._validate_integer_range(
            dep_conf_cfg.get('connection_limit'),
            'connection_limit',
            100,
            1,
            200
        )

        logger.debug("Config validation complete.")

    def _validate_proxies(self, proxies: List[str]) -> List[str]:
        """Validate proxy configuration."""
        if not isinstance(proxies, list):
            logger.warning("proxies must be a list. Using empty list.")
            return []
            
        valid_proxies = []
        for proxy in proxies:
            if not isinstance(proxy, str):
                logger.warning(f"Invalid proxy format: {proxy}. Must be string. Skipping.")
                continue
                
            if proxy.startswith(('http://', 'https://', 'socks4://', 'socks5://')):
                valid_proxies.append(proxy)
            else:
                logger.warning(f"Invalid proxy format: {proxy}. Must start with 'http://', 'https://', 'socks4://' or 'socks5://'. Skipping.")
        
        if not valid_proxies and proxies:
            logger.warning("No valid proxies found in configuration.")
        elif valid_proxies:
            logger.info(f"Using {len(valid_proxies)} proxies for HTTP requests.")
            
        return valid_proxies

    def _validate_email_addresses(self, addresses: List[str]) -> List[str]:
        """Validate email addresses."""
        if not isinstance(addresses, list):
            logger.warning("to_addrs must be a list. Using empty list.")
            return []
            
        valid_addresses = []
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
        for addr in addresses:
            if not isinstance(addr, str):
                logger.warning(f"Invalid email address format: {addr}. Must be string. Skipping.")
                continue
                
            if email_pattern.match(addr):
                valid_addresses.append(addr)
            else:
                logger.warning(f"Invalid email address format: {addr}. Skipping.")
                
        return valid_addresses

    def _validate_boolean(self, value: Any, key: str, default: bool) -> bool:
        """Validate boolean configuration value."""
        if not isinstance(value, bool):
            logger.warning(f"Invalid '{key}' (must be boolean). Defaulting to {default}.")
            return default
        return value

    def _validate_integer_range(self, value: Any, key: str, default: int, min_val: int, max_val: int) -> int:
        """Validate integer configuration value within range."""
        if not isinstance(value, int) or not (min_val <= value <= max_val):
            logger.warning(f"Invalid '{key}' (must be integer between {min_val} and {max_val}). Defaulting to {default}.")
            return default
        return value

    def _validate_top_packages(self, top_packages: Optional[Dict[str, List[str]]]) -> Dict[str, List[str]]:
        """Validate top packages configuration."""
        if not isinstance(top_packages, dict):
            logger.warning("top_packages must be a dictionary. Using defaults.")
            return copy.deepcopy(self.DEFAULT_CONFIG['dependency_confusion']['top_packages'])
            
        validated = {}
        for ecosystem, packages in top_packages.items():
            if not isinstance(packages, list):
                logger.warning(f"top_packages.{ecosystem} must be a list. Using defaults.")
                validated[ecosystem] = self.DEFAULT_CONFIG['dependency_confusion']['top_packages'].get(ecosystem, [])
                continue
                
            validated[ecosystem] = [str(pkg) for pkg in packages if pkg]
            
        return validated

    def _validate_registry_urls(self, registry_urls: Optional[Dict[str, str]]) -> Dict[str, str]:
        """Validate registry URLs configuration."""
        if not isinstance(registry_urls, dict):
            logger.warning("registry_urls must be a dictionary. Using defaults.")
            return copy.deepcopy(self.DEFAULT_CONFIG['dependency_confusion']['registry_urls'])
            
        validated = {}
        for ecosystem, url in registry_urls.items():
            if not isinstance(url, str) or not url.startswith(('http://', 'https://')):
                logger.warning(f"registry_urls.{ecosystem} must be a valid URL. Using default.")
                validated[ecosystem] = self.DEFAULT_CONFIG['dependency_confusion']['registry_urls'].get(ecosystem, '')
                continue
                
            validated[ecosystem] = url
            
        return validated

    def _validate_supported_files(self, supported_files: Optional[Dict[str, str]]) -> Dict[str, str]:
        """Validate supported files configuration."""
        if not isinstance(supported_files, dict):
            logger.warning("supported_files must be a dictionary. Using defaults.")
            return copy.deepcopy(self.DEFAULT_CONFIG['dependency_confusion']['supported_files'])
            
        validated = {}
        for filename, ecosystem in supported_files.items():
            if not isinstance(filename, str) or not isinstance(ecosystem, str):
                logger.warning(f"supported_files entry {filename} must have string filename and ecosystem. Skipping.")
                continue
                
            validated[filename] = ecosystem
            
        return validated

    def _validate_excluded_dirs(self, excluded_dirs: Optional[List[str]]) -> List[str]:
        """Validate excluded directories configuration."""
        if not isinstance(excluded_dirs, list):
            logger.warning("excluded_dirs must be a list. Using defaults.")
            return copy.deepcopy(self.DEFAULT_CONFIG['dependency_confusion']['excluded_dirs'])
            
        validated = []
        for directory in excluded_dirs:
            if not isinstance(directory, str) or not directory:
                logger.warning(f"excluded_dirs entry {directory} must be a non-empty string. Skipping.")
                continue
                
            validated.append(directory)
            
        return validated

    def get_config(self) -> Dict[str, Any]: return self.config
    def get_organizations(self) -> List[str]: orgs = self.config.get('organizations', []); return [str(o).strip() for o in orgs if str(o).strip()]
    
    def get_repo_identifier(self) -> 'RepositoryIdentifier':
        """Instantiate RepositoryIdentifier from current config."""
        from .repo_identifier import RepositoryIdentifier
        gh_cfg = self.config.get('github', {})
        return RepositoryIdentifier(
            github_token=gh_cfg.get('token'),
            github_tokens=gh_cfg.get('tokens', []),
            gitlab_url=self.config.get('gitlab', {}).get('api_url'),
            gitlab_token=self.config.get('gitlab', {}).get('token'),
            gitlab_enabled=self.config.get('gitlab', {}).get('enabled', False)
        )