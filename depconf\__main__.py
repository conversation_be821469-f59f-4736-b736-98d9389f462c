#!/usr/bin/env python3
"""
Entry point for `python -m depconf`
"""

import sys
import logging

try:
    from .cli import cli
except ImportError as e:
    print(f"Error: Cannot import package modules ({e}).", file=sys.stderr)
    print("Please run using 'python -m depconf check ...' or 'depconf check ...' after installation.", file=sys.stderr)
    sys.exit(1)
except Exception as e:
    print(f"An unexpected error occurred during import: {e}", file=sys.stderr)
    sys.exit(1)


def main() -> None:
    """
    Main entry point for the depconf tool.
    Sets up basic logging and calls the CLI handler.
    """
    # Minimal logger setup if run directly, setup_logging in cli handles the proper config
    if not logging.getLogger('depconf').hasHandlers():
        logging.basicConfig(
            level=logging.WARNING,
            format='%(levelname)s: %(message)s'
        )
    cli()


if __name__ == "__main__":
    main()
