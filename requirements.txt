# Core dependencies for DepConf Checker
# These are the minimum required dependencies for basic functionality

# CLI and configuration
click>=8.0.0
pyyaml>=6.0.0
colorama>=0.4.0

# HTTP requests and API interactions
requests>=2.25.0

# GitHub API
PyGithub>=1.55.0

# TOML parsing for Python < 3.11
tomli>=1.2.0; python_version < "3.11"

# Optional dependencies (install with pip install -e .[feature])
# These are defined in pyproject.toml but listed here for reference:

# GitLab support:
# python-gitlab>=3.0.0

# Async HTTP support:
# aiohttp>=3.8.0
# aiohttp-socks>=0.7.0  # For SOCKS proxy support

# Advanced typosquatting detection:
# python-Levenshtein>=0.12.0
# jellyfish>=0.9.0

# SOCKS proxy support:
# PySocks>=1.7.0

# Development dependencies (install with pip install -e .[dev])
# pytest>=6.0.0
# pytest-cov>=2.0.0
# black>=21.0.0
# flake8>=3.8.0
# mypy>=0.800
# pre-commit>=2.0.0

# Security note: Always pin versions in production deployments
# and regularly update dependencies to patch security vulnerabilities
