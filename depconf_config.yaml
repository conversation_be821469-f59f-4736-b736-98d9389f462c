general:
  log_level: INFO # debug, info, warning, error, critical
  output_dir: ./depconf_results

# --- Platform Credentials & Settings ---
github:
  # Required for listing org repos (especially private) and avoiding rate limits
  # SECURITY NOTE: Consider using environment variables instead of hardcoding tokens
  # Example: export GITHUB_TOKEN="your-token" and export GITHUB_TOKENS="token1,token2"
  tokens:
    - ****************************************
    - ****************************************
  # Rate limit handling settings
  rate_limit_retry: true
  rate_limit_retry_count: 3
  rate_limit_retry_delay: 60  # seconds
  rate_limit_retry_backoff: 2  # exponential backoff multiplier

gitlab:
  # Enable if scanning GitLab groups listed in 'organizations'
  enabled: false
  # Optional: Required for private GitLab groups/repos
  # SECURITY NOTE: Consider using environment variables instead of hardcoding tokens
  # Example: export GITLAB_TOKEN="your-token"
  token: "YOUR_GITLAB_PAT"
  # Optional: Override for self-hosted GitLab instances
  # Note: This overrides the empty default in config.py
  api_url: "https://gitlab.com/api/v4"

# --- Notification Settings ---
notifications:
  telegram:
    enabled: false
    bot_token: ""
    chat_id: ""
  discord:
    enabled: true
    webhook_url: "https://discord.com/api/webhooks/1368282240416481401/_OqpPy_Aw_QITD4QpGhW3zxtetFqjGTLVnS6G9CIm4j-jIYGEOb9fkfCmFt34gFUdivG"
  # Additional notification channels (using defaults from config.py)
  slack:
    enabled: false
    webhook_url: ""
  teams:
    enabled: false
    webhook_url: ""
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    smtp_username: ""
    smtp_password: ""
    from_address: ""
    to_addresses: []

# --- Dependency Confusion Check Settings ---
dependency_confusion:
  # Core settings
  enabled: true
  http_retries: 5  # Increased from 3
  http_timeout: 30  # Increased from 20
  enable_async: true
  async_ssl_verify: true
  max_concurrent_requests: 5  # Reduced from 10 to avoid rate limits
  connection_limit: 50  # Reduced from 100 to avoid rate limits
  request_delay: 1  # Added delay between requests in seconds

  # Rate limit handling
  rate_limit_handling:
    enabled: true
    retry_count: 3
    retry_delay: 60
    backoff_factor: 2
    max_retry_delay: 3600  # 1 hour max delay

  # Package exclusion lists
  exclude_packages: []  # General exclusion list
  exclude_packages_pip: []  # Python/pip package exclusions
  exclude_packages_npm: []  # NPM package exclusions
  exclude_packages_rubygems: []  # Ruby gem exclusions
  exclude_packages_maven: []  # Maven artifact exclusions
  exclude_packages_composer: []  # PHP/Composer package exclusions
  exclude_packages_gomodules: []  # Go module exclusions
  exclude_packages_nuget: []  # NuGet package exclusions

  # Internal package settings
  internal_package_pattern: ""  # Regex pattern for internal packages
  secure_namespaces: []  # List of trusted namespaces/scopes

  # Typosquatting detection
  enable_typosquatting_detection: true
  typosquatting_threshold: 2
  typosquatting_length_divisor: 4  # Added from config.py defaults

  # Proxy settings
  proxies: []  # List of proxy URLs
  proxy_rotation: false
  proxy_rotation_factor: 5

  # Registry and file settings (using defaults from config.py)
  # These settings will use the comprehensive defaults from config.py:
  # - DEFAULT_TOP_PACKAGES for top_packages
  # - REGISTRY_URLS for registry_urls
  # - DEFAULT_SUPPORTED_FILES for supported_files
  # - DEFAULT_EXCLUDED_DIRS for excluded_dirs
  # Uncomment and define only the settings you want to override:
  # top_packages: []  # Will use DEFAULT_TOP_PACKAGES if not specified
  # registry_urls: {}  # Will use REGISTRY_URLS if not specified
  # supported_files: []  # Will use DEFAULT_SUPPORTED_FILES if not specified
  # excluded_dirs: []  # Will use DEFAULT_EXCLUDED_DIRS if not specified

# --- Operation Settings ---
operation:
  # List of file patterns to consider as package files (glob format)
  package_file_patterns:
    - "package.json"
    - "requirements*.txt"
    - "pyproject.toml"
    - "Pipfile"
    - "Pipfile.lock"
    - "yarn.lock"
    - "package-lock.json"
    - "poetry.lock"
  concurrency: 2  # Reduced from 3 to avoid rate limits
  shallow_clone_timeout: 300  # 5 minutes
  scan_interval: 43200  # 12 hours (in seconds)
  batch_size: 10  # Added to process repositories in smaller batches
  batch_delay: 60  # Added delay between batches in seconds

# --- Target Organizations ---
organizations:
  # — HackerOne
  - grnhse
  - vimeo
  - ubiquiti
  - deriv-com
  - cargurus
  - rabobank
  - crypto-com
  - ford
  - omise
  - pixiv
  - relativitydev
  - flexport
  - muxinc
  - shutterfly
  - sage
  - figma
  - enjin
  - care-dot-com
  - pf-labs
  - SelfLender
  - scopely
  - salesloft
  - pigment
  - printful
  - ManoManoTech
  - sorare
  - razorpay
  - siriusxm
  - sideeffects
  - trendyol
  - kiwicom
  - calendly
  - biltrewards
  - mercadolibre
  - remitly
  - autodesk
  - klarna
  - flipkart
  - zooplus
  - Dotdashcom
  - quizlet
  - verisign
  - upwork
  - binance
  - kohlstechnology
  - etsy
  - meraki
  - optimizely
  - SiliconLabsSoftware
  - toloka
  - inspectorioinc
  - lightsparkdev
  - bykea
  - khealth
  - capitalone
  - circlefin
  - earlywarningproject
  - kohofinancial
  - chia-network
  - ArkoseLabs
  - fireblocks
  - dynatrace
  - tradingview
  - generalmotors
  - didacte
  - junelife
  - ecobee

  # — Bugcrowd
  - square
  - cloudinary
  - smartnews
  - ringcentral
  - washingtonpost
  - safaricom
  - GradConnection
  - dxc-technology
  - islandio
  - checkout
  - workiva
  - tryretool
  - autogeneral
  - samsara
  - talend
  - codatio
  - codecademy
  - checkr
  - rokudev
  - qlik-download
  - campaignmonitor
  - lightspeed
  - sellpoint
  - makeswift
  - m3ter-com
  - Storytel
  - myfitnesspal
  - trustpilot
  - AxisCommunications
  - paysafegroup
  - paysafecard
  - rewindio
  - thetradedesk
  - sproutsocial
  - moneytree
  - soundcloud
  - sophos
  - bitdefender
  - iRobotEducation
  - transferwise
  - Netgear
  - ibotta
  - viatorinc
  - pantheon-systems
  - indeedeng
  - pinterest
  - teslamotors
  - hubspot
  - Acornsgrow
  - twilio
  - Stellantis
  - lastpass
  - okta
  - certinia
  - fis-components
  - flourishlib
  - kaleido-io
  - octopusdeploy
  - operasoftware
  - tripactions
  - monash-merc
  - unity-technologies
  - Gearset
  - pexels
  - Pixabay
  - elementor
  - backblaze
  - luno
  - bigcommerce
  - kitworks-systems
  - ifood
  - asana
  - expressvpn
  - onetrust
  - 20minutes
  - electroneum
  - balsamiq
  - eazybi
  - majidalfuttaim
  - K15t
  - lululemon
  - justeattakeaway
  - opsgenie
  - code-dot-org
  - canva
  - htdc
  - tripadvisor
  - origin-energy
  - skyscanner
  - tidal-music
  - immutable
  - comcast
  - skroutz
  - codecrafters-io
  - thousandeyes
  - DELL
  - imperva
  - mattermost
  - gleanwork
  - bitgo
  - OrderlyNetwork
  - TheHutGroup
  - newrelic
  - sportradar
  - kucoin
  - infiniteathlete
  - auth0
  - motorolamobilityllc
  - epam
  - aruba
  - bitpanda-labs
  - digidotcom
  - ynab
  - exoscale
  - VantaInc
  - digicert
  - codeforamerica
  - barracudanetworks
  - moovit
  - catawiki
  - seek-oss
  - chime
  - saltlabsinc
  - quintoandar

  # — BugBounty.ch
  - airlock
  - abraxas-labs
  - baloise
  - republique-et-canton-de-geneve
  - diemobiliar

  # — HackenProof
  - 1inch
  - allbridge-io
  - assetmantle
  - bitmartexchange
  - Bitrue-exchange
  - blofin
  - btsecom
  - chainstack
  - coin98
  - coinmetro
  - coingecko
  - DeltaPrimeLabs
  - dexalot
  - difx
  - exmo-dev
  - Filmio-Inc
  - gateio
  - horizon-protocol
  - impermax-finance
  - iotexproject
  - JOJOexchange
  - Kinza-Finance
  - KolibriOS
  - latoken
  - unioncredit
  - sense-finance
  - napierfi
  - axis-fi
  - dhedge
  - exactly
  - satlayer
  - Mach-Finance
  - sherlock-protocol
  - equilibria-xyz
  - SYMM-IO
  - ston-fi
  - econia-labs
  - Aegis-im
  - Zilliqa
  - ascendex
  - pinto-org
  - MystenLabs
  - myetherwallet
  - near
  - okx
  - Plisio
  - 0xpolygon
  - poloniex
  - shidoglobal
  - tetu-io
  - capsule-corp-ternoa
  - vechain
  - vertex-protocol
  - whitebit-exchange
  - WhiteMarketPodcast
  - xt
  - zeta-chain

  # — immunefi
  - lidofinance
  - stacks-sbtc
  - Bonfida
  - deri-protocol
  - ensdomains
  - reserve-protocol
  - omni-network
  - pyth-network
  - roycoprotocol
  - term-structure
  - babylonlabs-io
  - morpho-org
  - ethena-labs
  - berachain
  - threshold-network
  - vesuxyz
  - Layr-Labs
  - inversefinance
  - mux-world
  - OpenZeppelin
  - alchemix-finance
  - compound-finance
  - forta-network
  - pantos-io
  - aave
  - Kelp-DAO
  - rhinofi
  - hyperlane-xyz
  - 0xPolygonHermez
  - axelarnetwork
  - availproject
  - firedancer-io
  - YieldLayer
  - flare-foundation
  - wormhole-foundation
  - ava-labs
  - defisaver
  - metastreet-labs
  - AmbireTech
  - AstarNetwork
  - OffchainLabs
  - Consensys
  - hiero-ledger
  - symbioticfi
  - bobanetwork
  - HorizenOfficial
  - Folks-Finance
  - jito-foundation
  - LayerZero-Labs
  - interlay
  - NodleCode
  - informalsystems
  - AcalaNetwork
  - nayms
  - balmy-protocol
  - Lightprotocol
  - Synthetixio
  - anvil-works
  - polymarket
  - oasisprotocol
  - Bond-Protocol
  - QuadrataNetwork
  - ankr-network
  - kilnfi
  - stargate-protocol
  - matter-labs
  - sparkdotfi
  - superform-xyz
  - tinymanorg
  - dforce-network
  - sora-xor
  - integritee-network
  - neutron-org
  - 0xProject
  - yieldnest
  - zerolend
  - sei-protocol
  - lombard-finance
  - balancer
  - Zest-Protocol
  - IPOR-Labs
  - cowprotocol
  - instadapp
  - hathornetwork
  - distributedcollective
  - gearbox-protocol
  - gear-tech
  - charmfinance
  - galacticcouncil
  - alpaca-finance
  - cardano-foundation
  - starkware-libs
  - bitcoin-sv
  - bifrost-io
  - astroport-fi
  - cardinal-cryptography
  - degatedev
  - ethereum-lists
  - buttonwood-protocol
  - pontem-network
  - acalanetwork
  - vt-btf
  - alexgo-io
  - harvestfi
  - yamatoprotocol
  - flamingo-finance
  - impossiblefinance
  - mtpelerin
  - 88mphapp
  - dodoex
  - sweatco
  - tranchess
  - neo-project
  - serai-dex
  - opynfinance
  - econia-labs
  - coreumfoundation
  - push-protocol
  - scroll-tech
  - orca-so
  - revert-finance
  - marinade-finance
  - pactfi
  - ichifarm
  - backstop-protocol
  - nexusmutual
  - poolshark-protocol
  - range-protocol
  - bancorprotocol
  - stellar
  - icon-project
  - byteball
  - raydium-io
  - kadena-io
  - rocket-pool
  - paribus
  - money-on-chain
  - polymeshassociation
  - stafiprotocol

  # — Google Orgs
  - bazelbuild
  - angular
  - golang
  - protocolbuffers
  - googleapis
  - googlesamples
  - googlechrome
  - googlefonts
  - google-research
  - google-coral
  - googleads
  - googleanalytics
  - googleworkspace
  - googlemaps
  - google-pay
  - GoogleCloudPlatform
  - tensorflow
  - flutter
  - firebase
  - material-components
  - chromium
  - googlecreativelab
  - grpc