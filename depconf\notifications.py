# depconf/notifications.py
from . import __version__
import json
import logging
import requests
import time
import smtplib
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Dict, Any, Optional, Tuple

from colorama import Fore, Style

logger = logging.getLogger('depconf.notifications')

class NotificationManager:
    """Class for sending notifications about dependency confusion findings."""

    def __init__(self, notifications_settings: Dict[str, Any]):
        """Initialize the notification manager."""
        # self.config = config or {}
        # notifications_cfg = self.config
        self.http_timeout = notifications_settings.get('http_timeout', 20)

        # Telegram
        telegram_cfg = notifications_settings.get('telegram', {})
        self.telegram_enabled = telegram_cfg.get('enabled', False)
        self.telegram_bot_token = telegram_cfg.get('bot_token')
        self.telegram_chat_id = telegram_cfg.get('chat_id')
        if self.telegram_enabled and (not self.telegram_bot_token or not self.telegram_chat_id):
            logger.error(f"{Fore.RED}❌ Telegram enabled but token/chat_id missing! Disabling.{Style.RESET_ALL}")
            self.telegram_enabled = False
        elif self.telegram_enabled:
            logger.info(f"🔔 Telegram notifications enabled for chat ID: {self.telegram_chat_id}")

        # Discord
        discord_cfg = notifications_settings.get('discord', {})
        self.discord_enabled = discord_cfg.get('enabled', False)
        self.discord_webhook_url = discord_cfg.get('webhook_url')
        if self.discord_enabled and (not self.discord_webhook_url or not self.discord_webhook_url.startswith('https://discord.com/api/webhooks/')):
            logger.error(f"{Fore.RED}❌ Discord enabled but webhook_url is missing or invalid! Disabling.{Style.RESET_ALL}")
            self.discord_enabled = False
        elif self.discord_enabled:
            logger.info(f"🔔 Discord notifications enabled.")

        # Slack
        slack_cfg = notifications_settings.get('slack', {})
        self.slack_enabled = slack_cfg.get('enabled', False)
        self.slack_webhook_url = slack_cfg.get('webhook_url')
        self.slack_channel = slack_cfg.get('channel')
        if self.slack_enabled and not self.slack_webhook_url:
            logger.error(f"{Fore.RED}❌ Slack enabled but webhook_url missing! Disabling.{Style.RESET_ALL}")
            self.slack_enabled = False
        elif self.slack_enabled:
            logger.info(f"🔔 Slack notifications enabled.")

        # Teams
        teams_cfg = notifications_settings.get('teams', {})
        self.teams_enabled = teams_cfg.get('enabled', False)
        self.teams_webhook_url = teams_cfg.get('webhook_url')
        if self.teams_enabled and not self.teams_webhook_url:
            logger.error(f"{Fore.RED}❌ Teams enabled but webhook_url missing! Disabling.{Style.RESET_ALL}")
            self.teams_enabled = False
        elif self.teams_enabled:
            logger.info(f"🔔 Teams notifications enabled.")

        # Email
        email_cfg = notifications_settings.get('email', {})
        self.email_enabled = email_cfg.get('enabled', False)
        self.smtp_server = email_cfg.get('smtp_server')
        self.smtp_port = email_cfg.get('smtp_port', 587)
        self.smtp_username = email_cfg.get('username')
        self.smtp_password = email_cfg.get('password')
        self.email_from = email_cfg.get('from_addr')
        self.email_to = email_cfg.get('to_addrs', [])
        
        if self.email_enabled:
            if not all([self.smtp_server, self.email_from, self.email_to]):
                logger.error(f"{Fore.RED}❌ Email enabled but missing required fields (server/from/to)! Disabling.{Style.RESET_ALL}")
                self.email_enabled = False
            elif not isinstance(self.email_to, list) or not self.email_to:
                logger.error(f"{Fore.RED}❌ Email enabled but to_addrs is empty or not a list! Disabling.{Style.RESET_ALL}")
                self.email_enabled = False
            else:
                logger.info(f"🔔 Email notifications enabled for {len(self.email_to)} recipients.")

    def is_notification_enabled(self) -> bool:
        """Checks if any notification platform is enabled."""
        return (self.telegram_enabled or self.discord_enabled or 
                self.slack_enabled or self.teams_enabled or self.email_enabled)

    def _escape_telegram_markdown_v2(self, text: str) -> str:
        """Escapes text for Telegram MarkdownV2"""
        if not isinstance(text, str): text = str(text)
        escape_chars = r'_*[]()~`>#+-=|{}.!'
        for char in escape_chars:
            text = text.replace(char, f'\\{char}')
        return text

    async def send_telegram_message(self, message: str) -> bool:
        if not self.telegram_enabled: return False
        escaped_message = self._escape_telegram_markdown_v2(message)
        max_len = 4096
        if len(escaped_message) > max_len:
            trunc_point = escaped_message.rfind('\n', 0, max_len - 40)
            trunc_point = trunc_point if trunc_point != -1 else max_len - 40
            escaped_message = escaped_message[:trunc_point] + "\n\n_\\.\\.\\. \\(Message truncated\\)_"
            logger.warning("Truncated long Telegram message.")

        api_url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
        payload = {'chat_id': self.telegram_chat_id, 'text': escaped_message, 'parse_mode': 'MarkdownV2'}
        headers = {'Content-Type': 'application/json'}

        try:
            async def _send():
                resp = requests.post(api_url, json=payload, headers=headers, timeout=self.http_timeout)
                resp_data = {}
                try: resp_data = resp.json()
                except json.JSONDecodeError: pass

                if resp.status_code == 429:
                    retry_after = int(resp_data.get('parameters', {}).get('retry_after', 5))
                    logger.warning(f"⏳ Telegram rate limited. Waiting {retry_after}s...")
                    time.sleep(retry_after + 1)
                    resp = requests.post(api_url, json=payload, headers=headers, timeout=self.http_timeout)
                    try: resp_data = resp.json()
                    except json.JSONDecodeError: pass

                resp.raise_for_status()
                logger.info(f"✅ Telegram message sent.")
                return True

            return await asyncio.to_thread(_send)
        except requests.exceptions.Timeout:
            logger.error(f"{Fore.RED}❌ Timeout sending Telegram notification.{Style.RESET_ALL}")
            return False
        except requests.exceptions.RequestException as e:
            error_detail = self._get_error_detail(e)
            logger.error(f"{Fore.RED}❌ Error sending Telegram notification: {error_detail}{Style.RESET_ALL}")
            return False
        except Exception as e:
            logger.error(f"💥 Unexpected error sending Telegram message: {e}", exc_info=True)
            return False

    async def send_discord_message(self, message: str, embeds: Optional[List[Dict[str, Any]]] = None) -> bool:
        if not self.discord_enabled: return False

        payload: Dict[str, Any] = {}
        if message:
            if len(message) > 2000:
                message = message[:1990] + "...(truncated)"
            payload['content'] = message
        if embeds:
            if len(embeds) > 10:
                logger.warning(f"Discord allows max 10 embeds, got {len(embeds)}. Truncating.")
                embeds = embeds[:10]
            payload['embeds'] = embeds

        if not payload:
            logger.warning("Attempted to send empty Discord message.")
            return False

        try:
            async def _send():
                resp = requests.post(self.discord_webhook_url, json=payload, timeout=self.http_timeout)
                resp_data = {}
                try: resp_data = resp.json()
                except json.JSONDecodeError: pass

                if resp.status_code == 429:
                    retry_after_ms = resp_data.get('retry_after', 5000)
                    retry_after_s = retry_after_ms / 1000.0
                    logger.warning(f"⏳ Discord rate limited. Waiting {retry_after_s:.1f}s...")
                    time.sleep(retry_after_s + 0.5)
                    resp = requests.post(self.discord_webhook_url, json=payload, timeout=self.http_timeout)
                    try: resp_data = resp.json()
                    except json.JSONDecodeError: pass

                resp.raise_for_status()
                logger.info(f"✅ Discord message sent.")
                return True

            return await asyncio.to_thread(_send)
        except requests.exceptions.Timeout:
            logger.error(f"{Fore.RED}❌ Timeout sending Discord notification.{Style.RESET_ALL}")
            return False
        except requests.exceptions.RequestException as e:
            error_detail = self._get_error_detail(e)
            logger.error(f"{Fore.RED}❌ Error sending Discord notification: {error_detail}{Style.RESET_ALL}")
            return False
        except Exception as e:
            logger.error(f"💥 Unexpected error sending Discord message: {e}", exc_info=True)
            return False

    async def send_slack_message(self, message_payload: Dict[str, Any]) -> bool:
        """Send a message to Slack using webhook."""
        if not self.slack_enabled: return False

        try:
            async def _send():
                resp = requests.post(self.slack_webhook_url, json=message_payload, timeout=self.http_timeout)
                resp_data = {}
                try: resp_data = resp.json()
                except json.JSONDecodeError: pass

                if resp.status_code == 429:
                    retry_after = int(resp_data.get('retry_after', 5))
                    logger.warning(f"⏳ Slack rate limited. Waiting {retry_after}s...")
                    time.sleep(retry_after + 1)
                    resp = requests.post(self.slack_webhook_url, json=message_payload, timeout=self.http_timeout)
                    try: resp_data = resp.json()
                    except json.JSONDecodeError: pass

                resp.raise_for_status()
                logger.info(f"✅ Slack message sent.")
                return True

            return await asyncio.to_thread(_send)
        except requests.exceptions.Timeout:
            logger.error(f"{Fore.RED}❌ Timeout sending Slack notification.{Style.RESET_ALL}")
            return False
        except requests.exceptions.RequestException as e:
            error_detail = self._get_error_detail(e)
            logger.error(f"{Fore.RED}❌ Error sending Slack notification: {error_detail}{Style.RESET_ALL}")
            return False
        except Exception as e:
            logger.error(f"💥 Unexpected error sending Slack message: {e}", exc_info=True)
            return False

    async def send_teams_message(self, message_payload: Dict[str, Any]) -> bool:
        """Send a message to Microsoft Teams using webhook."""
        if not self.teams_enabled: return False

        try:
            async def _send():
                resp = requests.post(self.teams_webhook_url, json=message_payload, timeout=self.http_timeout)
                resp_data = {}
                try: resp_data = resp.json()
                except json.JSONDecodeError: pass

                if resp.status_code == 429:
                    retry_after = int(resp_data.get('retry_after', 5))
                    logger.warning(f"⏳ Teams rate limited. Waiting {retry_after}s...")
                    time.sleep(retry_after + 1)
                    resp = requests.post(self.teams_webhook_url, json=message_payload, timeout=self.http_timeout)
                    try: resp_data = resp.json()
                    except json.JSONDecodeError: pass

                resp.raise_for_status()
                logger.info(f"✅ Teams message sent.")
                return True

            return await asyncio.to_thread(_send)
        except requests.exceptions.Timeout:
            logger.error(f"{Fore.RED}❌ Timeout sending Teams notification.{Style.RESET_ALL}")
            return False
        except requests.exceptions.RequestException as e:
            error_detail = self._get_error_detail(e)
            logger.error(f"{Fore.RED}❌ Error sending Teams notification: {error_detail}{Style.RESET_ALL}")
            return False
        except Exception as e:
            logger.error(f"💥 Unexpected error sending Teams message: {e}", exc_info=True)
            return False

    async def send_email_message(self, subject: str, body_html: str, body_text: str) -> bool:
        """Send an email message."""
        if not self.email_enabled: return False

        try:
            async def _send():
                msg = MIMEMultipart('alternative')
                msg['Subject'] = subject
                msg['From'] = self.email_from
                msg['To'] = ', '.join(self.email_to)

                msg.attach(MIMEText(body_text, 'plain'))
                msg.attach(MIMEText(body_html, 'html'))

                with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                    server.starttls()
                    if self.smtp_username and self.smtp_password:
                        server.login(self.smtp_username, self.smtp_password)
                    server.send_message(msg)
                    logger.info(f"✅ Email sent to {len(self.email_to)} recipients.")
                    return True

            return await asyncio.to_thread(_send)
        except Exception as e:
            logger.error(f"{Fore.RED}❌ Error sending email: {str(e)}{Style.RESET_ALL}")
            return False

    def _get_error_detail(self, e: requests.exceptions.RequestException) -> str:
        """Extract detailed error information from a RequestException."""
        if hasattr(e, 'response') and e.response is not None:
            try:
                return f"{e.response.status_code}: {e.response.json()}"
            except:
                return f"{e.response.status_code}: {e.response.text}"
        return str(e)

    def format_findings_telegram(self, repo_info: Dict[str, Any], findings_list: List[Dict], finding_type: str) -> str:
        """Formats findings results for Telegram."""
        lines: List[str] = []
        repo_name = repo_info.get('full_name', 'Unknown Target')
        org_name = repo_info.get('organization', None)
        platform = repo_info.get('platform', 'unknown')
        platform_icon = "🐙" if platform == 'github' else ("🦊" if platform == 'gitlab' else "❓")

        # Set title based on finding type
        if finding_type == 'dependency_confusion':
            title_icon = "🚨"
            title_verb = "Potential Dependency Confusion Issues Found"
        elif finding_type == 'public_not_found':
            title_icon = "🔍"
            title_verb = "Public Packages Not Found Issues"
        else:  # typosquatting
            title_icon = "🎯"
            title_verb = "Potential Typosquatting Issues Found"

        lines.append(f"{title_icon} *{title_verb} in* `{repo_name}` {platform_icon}")
        if org_name: lines.append(f"*Organization:* `{org_name}`")

        total_issues = len(findings_list)
        lines.append(f"*Total Issues:* {total_issues}")

        if total_issues > 0: lines.append("")

        dep_limit = 20
        items_shown = 0
        files_shown = 0
        file_limit = 5

        # Group findings by file
        findings_by_file: Dict[str, List[Dict]] = {}
        for finding in findings_list:
            file = finding.get('file', 'Unknown File')
            if file not in findings_by_file:
                findings_by_file[file] = []
            findings_by_file[file].append(finding)

        for file, file_findings in findings_by_file.items():
            if files_shown >= file_limit: break
            if not file_findings: continue

            files_shown += 1
            lines.append(f"📄 *File:* `{file}`")
            
            for finding in file_findings[:max(0, dep_limit - items_shown)]:
                if finding_type == 'typosquatting':
                    similar_to = finding.get('similar_to', [])
                    similar_str = ', '.join(f"`{pkg}`" for pkg in similar_to)
                    lines.append(f"  ↳ Package: `{finding['package']}` (similar to: {similar_str})")
                else:
                    reason = finding.get('reason', 'Unknown reason')
                    lines.append(f"  ↳ Package: `{finding['package']}` ({reason})")
                items_shown += 1

            if len(file_findings) > (dep_limit - items_shown):
                remaining = len(file_findings) - (dep_limit - items_shown)
                lines.append(f"  ↳ _... and {remaining} more in this file_")
            if items_shown >= dep_limit: break

        if total_issues > items_shown:
            lines.append(f"\n_... and {total_issues - items_shown} more issues in other files._")

        url = repo_info.get('html_url')
        if url:
            lines.append("")
            url_cleaned = url.replace(')', '%29').replace('(', '%28')
            lines.append(f"[View Repository]({url_cleaned})")

        return "\n".join(lines)

    def format_findings_discord(self, repo_info: Dict[str, Any], findings_list: List[Dict], finding_type: str) -> Tuple[str, Optional[Dict[str, Any]]]:
        """Formats findings results for Discord."""
        repo_name = repo_info.get('full_name', 'Unknown Target')
        org_name = repo_info.get('organization', None)
        platform = repo_info.get('platform', 'unknown')
        repo_url = repo_info.get('html_url', '#')
        platform_icon = "🐙" if platform == 'github' else ("🦊" if platform == 'gitlab' else "❓")

        # Set title based on finding type
        if finding_type == 'dependency_confusion':
            title_icon = "🚨"
            title_verb = "Potential Dependency Confusion Issues Found"
            embed_title = "Dependency Confusion Details"
            embed_color = 0xFF470F  # Orangey-Red
        elif finding_type == 'public_not_found':
            title_icon = "🔍"
            title_verb = "Public Packages Not Found Issues"
            embed_title = "Public Packages Not Found Details"
            embed_color = 0x3498DB  # Blue
        else:  # typosquatting
            title_icon = "🎯"
            title_verb = "Potential Typosquatting Issues Found"
            embed_title = "Typosquatting Details"
            embed_color = 0x9B59B6  # Purple

        content_msg = f"{title_icon} **{title_verb} in `{repo_name}`** {platform_icon}"
        total_issues = len(findings_list)

        embed_desc_parts = []
        if org_name: embed_desc_parts.append(f"**Organization:** `{org_name}`")
        embed_desc_parts.append(f"**Total Issues Found:** {total_issues}")

        embed = {
            'title': f"{title_icon} {embed_title}: {repo_name}",
            'url': repo_url,
            'color': embed_color,
            'description': "\n".join(embed_desc_parts),
            'fields': [],
            'timestamp': time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime()),
            'footer': {'text': f'depconf-checker v{__version__}'}
        }

        fields_limit = 25
        value_char_limit = 1024
        name_char_limit = 256
        fields_added = 0

        # Group findings by file
        findings_by_file: Dict[str, List[Dict]] = {}
        for finding in findings_list:
            file = finding.get('file', 'Unknown File')
            if file not in findings_by_file:
                findings_by_file[file] = []
            findings_by_file[file].append(finding)

        for file, file_findings in findings_by_file.items():
            if fields_added >= fields_limit: break
            if not file_findings: continue

            field_name = f"📄 File: `{file}`"
            if len(field_name) > name_char_limit:
                field_name = field_name[:name_char_limit-4] + "...`"

            field_parts = []
            for finding in file_findings:
                if finding_type == 'typosquatting':
                    similar_to = finding.get('similar_to', [])
                    similar_str = ', '.join(f"`{pkg}`" for pkg in similar_to)
                    field_parts.append(f"• `{finding['package']}` (similar to: {similar_str})")
                else:
                    reason = finding.get('reason', 'Unknown reason')
                    field_parts.append(f"• `{finding['package']}` ({reason})")

            field_value = "\n".join(field_parts)
            if len(field_value) > value_char_limit:
                trunc_point = field_value.rfind('\n', 0, value_char_limit - 20)
                trunc_point = trunc_point if trunc_point != -1 else value_char_limit - 20
                field_value = field_value[:trunc_point] + "\n... *(truncated)*"

            embed['fields'].append({'name': field_name, 'value': field_value, 'inline': False})
            fields_added += 1

        if total_issues > sum(len(field['value'].split('\n')) for field in embed['fields']):
            if 'footer' in embed:
                embed['footer']['text'] += f" | Showing details for first {fields_added} files/fields."

        return content_msg, embed

    def format_findings_slack(self, repo_info: Dict[str, Any], findings_list: List[Dict], finding_type: str) -> Dict[str, Any]:
        """Formats findings results for Slack."""
        repo_name = repo_info.get('full_name', 'Unknown Target')
        org_name = repo_info.get('organization', None)
        platform = repo_info.get('platform', 'unknown')
        repo_url = repo_info.get('html_url', '#')
        platform_icon = "🐙" if platform == 'github' else ("🦊" if platform == 'gitlab' else "❓")

        # Set title based on finding type
        if finding_type == 'dependency_confusion':
            title_icon = "🚨"
            title_verb = "Potential Dependency Confusion Issues Found"
            color = "#FF470F"  # Orangey-Red
        elif finding_type == 'public_not_found':
            title_icon = "🔍"
            title_verb = "Public Packages Not Found Issues"
            color = "#3498DB"  # Blue
        else:  # typosquatting
            title_icon = "🎯"
            title_verb = "Potential Typosquatting Issues Found"
            color = "#9B59B6"  # Purple

        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"{title_icon} {title_verb} in {repo_name} {platform_icon}"
                }
            }
        ]

        if org_name:
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Organization:* `{org_name}`"
                }
            })

        # Group findings by file
        findings_by_file: Dict[str, List[Dict]] = {}
        for finding in findings_list:
            file = finding.get('file', 'Unknown File')
            if file not in findings_by_file:
                findings_by_file[file] = []
            findings_by_file[file].append(finding)

        for file, file_findings in findings_by_file.items():
            if not file_findings: continue

            file_text = f"*📄 File:* `{file}`\n"
            for finding in file_findings:
                if finding_type == 'typosquatting':
                    similar_to = finding.get('similar_to', [])
                    similar_str = ', '.join(f"`{pkg}`" for pkg in similar_to)
                    file_text += f"• `{finding['package']}` (similar to: {similar_str})\n"
                else:
                    reason = finding.get('reason', 'Unknown reason')
                    file_text += f"• `{finding['package']}` ({reason})\n"

            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": file_text
                }
            })

        if repo_url:
            blocks.append({
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"<{repo_url}|View Repository>"
                    }
                ]
            })

        return {
            "blocks": blocks,
            "text": f"{title_icon} {title_verb} in {repo_name}"  # Fallback text
        }

    def format_findings_teams(self, repo_info: Dict[str, Any], findings_list: List[Dict], finding_type: str) -> Dict[str, Any]:
        """Formats findings results for Teams."""
        repo_name = repo_info.get('full_name', 'Unknown Target')
        org_name = repo_info.get('organization', None)
        platform = repo_info.get('platform', 'unknown')
        repo_url = repo_info.get('html_url', '#')
        platform_icon = "🐙" if platform == 'github' else ("🦊" if platform == 'gitlab' else "❓")

        # Set title based on finding type
        if finding_type == 'dependency_confusion':
            title_icon = "🚨"
            title_verb = "Potential Dependency Confusion Issues Found"
            accent_color = "#FF470F"  # Orangey-Red
        elif finding_type == 'public_not_found':
            title_icon = "🔍"
            title_verb = "Public Packages Not Found Issues"
            accent_color = "#3498DB"  # Blue
        else:  # typosquatting
            title_icon = "🎯"
            title_verb = "Potential Typosquatting Issues Found"
            accent_color = "#9B59B6"  # Purple

        # Group findings by file
        findings_by_file: Dict[str, List[Dict]] = {}
        for finding in findings_list:
            file = finding.get('file', 'Unknown File')
            if file not in findings_by_file:
                findings_by_file[file] = []
            findings_by_file[file].append(finding)

        facts = []
        if org_name:
            facts.append({
                "name": "Organization",
                "value": org_name
            })

        facts.append({
            "name": "Total Issues",
            "value": str(len(findings_list))
        })

        body = []
        for file, file_findings in findings_by_file.items():
            if not file_findings: continue

            file_text = f"📄 File: {file}\n\n"
            for finding in file_findings:
                if finding_type == 'typosquatting':
                    similar_to = finding.get('similar_to', [])
                    similar_str = ', '.join(similar_to)
                    file_text += f"• {finding['package']} (similar to: {similar_str})\n"
                else:
                    reason = finding.get('reason', 'Unknown reason')
                    file_text += f"• {finding['package']} ({reason})\n"

            body.append({
                "type": "TextBlock",
                "text": file_text,
                "wrap": True
            })

        if repo_url:
            body.append({
                "type": "ActionSet",
                "actions": [
                    {
                        "type": "Action.OpenUrl",
                        "title": "View Repository",
                        "url": repo_url
                    }
                ]
            })

        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.0",
                        "body": [
                            {
                                "type": "Container",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": f"{title_icon} {title_verb} in {repo_name} {platform_icon}",
                                        "weight": "Bolder",
                                        "size": "Large"
                                    },
                                    {
                                        "type": "FactSet",
                                        "facts": facts
                                    }
                                ]
                            },
                            *body
                        ],
                        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json"
                    }
                }
            ]
        }

    def format_findings_email(self, repo_info: Dict[str, Any], findings_list: List[Dict], finding_type: str) -> Tuple[str, str, str]:
        """Formats findings results for email."""
        repo_name = repo_info.get('full_name', 'Unknown Target')
        org_name = repo_info.get('organization', None)
        platform = repo_info.get('platform', 'unknown')
        repo_url = repo_info.get('html_url', '#')
        platform_icon = "🐙" if platform == 'github' else ("🦊" if platform == 'gitlab' else "❓")

        # Set title based on finding type
        if finding_type == 'dependency_confusion':
            title_icon = "🚨"
            title_verb = "Potential Dependency Confusion Issues Found"
        elif finding_type == 'public_not_found':
            title_icon = "🔍"
            title_verb = "Public Packages Not Found Issues"
        else:  # typosquatting
            title_icon = "🎯"
            title_verb = "Potential Typosquatting Issues Found"

        subject = f"{title_icon} {title_verb} in {repo_name}"

        # Group findings by file
        findings_by_file: Dict[str, List[Dict]] = {}
        for finding in findings_list:
            file = finding.get('file', 'Unknown File')
            if file not in findings_by_file:
                findings_by_file[file] = []
            findings_by_file[file].append(finding)

        # HTML body
        html_parts = [
            f"<h1>{title_icon} {title_verb} in {repo_name} {platform_icon}</h1>"
        ]

        if org_name:
            html_parts.append(f"<p><strong>Organization:</strong> {org_name}</p>")

        html_parts.append(f"<p><strong>Total Issues Found:</strong> {len(findings_list)}</p>")
        html_parts.append("<hr>")

        for file, file_findings in findings_by_file.items():
            if not file_findings: continue

            html_parts.append(f"<h2>📄 File: {file}</h2>")
            html_parts.append("<ul>")
            for finding in file_findings:
                if finding_type == 'typosquatting':
                    similar_to = finding.get('similar_to', [])
                    similar_str = ', '.join(similar_to)
                    html_parts.append(f"<li><code>{finding['package']}</code> (similar to: {similar_str})</li>")
                else:
                    reason = finding.get('reason', 'Unknown reason')
                    html_parts.append(f"<li><code>{finding['package']}</code> ({reason})</li>")
            html_parts.append("</ul>")

        if repo_url:
            html_parts.append(f'<p><a href="{repo_url}">View Repository</a></p>')

        html_body = "\n".join(html_parts)

        # Plain text body
        text_parts = [
            f"{title_icon} {title_verb} in {repo_name} {platform_icon}"
        ]

        if org_name:
            text_parts.append(f"Organization: {org_name}")

        text_parts.append(f"Total Issues Found: {len(findings_list)}")
        text_parts.append("---")

        for file, file_findings in findings_by_file.items():
            if not file_findings: continue

            text_parts.append(f"📄 File: {file}")
            for finding in file_findings:
                if finding_type == 'typosquatting':
                    similar_to = finding.get('similar_to', [])
                    similar_str = ', '.join(similar_to)
                    text_parts.append(f"• {finding['package']} (similar to: {similar_str})")
                else:
                    reason = finding.get('reason', 'Unknown reason')
                    text_parts.append(f"• {finding['package']} ({reason})")
            text_parts.append("")

        if repo_url:
            text_parts.append(f"View Repository: {repo_url}")

        text_body = "\n".join(text_parts)

        return subject, html_body, text_body

    async def notify_findings(self, repo_info: Dict[str, Any], findings_list: List[Dict], finding_type: str = 'dependency_confusion') -> None:
        """Sends notifications for findings."""
        if not self.is_notification_enabled(): return
        if not findings_list: return

        repo_name = repo_info.get('full_name', 'Unknown Target')
        issue_count = len(findings_list)
        
        # Set log message based on finding type
        if finding_type == 'dependency_confusion':
            log_msg = f"  📮 Sending notification for {issue_count} potential dependency confusion issues in {repo_name}"
        elif finding_type == 'public_not_found':
            log_msg = f"  📮 Sending notification for {issue_count} public packages not found in {repo_name}"
        else:  # typosquatting
            log_msg = f"  📮 Sending notification for {issue_count} potential typosquatting issues in {repo_name}"
            
        logger.info(log_msg)

        # Throttle slightly between different platforms
        sent_platform = False

        # Telegram
        if self.telegram_enabled:
            try:
                tg_message = self.format_findings_telegram(repo_info, findings_list, finding_type)
                if tg_message:
                    await self.send_telegram_message(tg_message)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Telegram notification for {repo_name}: {e}", exc_info=True)

        # Discord
        if self.discord_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                content, embed = self.format_findings_discord(repo_info, findings_list, finding_type)
                if content or embed:
                    await self.send_discord_message(content, [embed] if embed else None)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Discord notification for {repo_name}: {e}", exc_info=True)

        # Slack
        if self.slack_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                slack_payload = self.format_findings_slack(repo_info, findings_list, finding_type)
                if slack_payload:
                    await self.send_slack_message(slack_payload)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Slack notification for {repo_name}: {e}", exc_info=True)

        # Teams
        if self.teams_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                teams_payload = self.format_findings_teams(repo_info, findings_list, finding_type)
                if teams_payload:
                    await self.send_teams_message(teams_payload)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Teams notification for {repo_name}: {e}", exc_info=True)

        # Email
        if self.email_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                subject, html_body, text_body = self.format_findings_email(repo_info, findings_list, finding_type)
                if subject and (html_body or text_body):
                    await self.send_email_message(subject, html_body, text_body)
            except Exception as e:
                logger.error(f"Failed formatting/sending email notification for {repo_name}: {e}", exc_info=True)

    async def send_test_notification(self) -> bool:
        """Sends a test notification with example findings for all finding types."""
        platforms_tested = 0
        success_count = 0
        test_repo_info = {
            'full_name': 'TestOrg/TestRepo',
            'name': 'TestRepo',
            'organization': 'TestOrg',
            'platform': 'github',
            'html_url': 'https://github.com/TestOrg/TestRepo',
            'clone_url': 'https://github.com/TestOrg/TestRepo.git',
            'latest_commit_sha': 'a1b2c3d4e5f6'
        }
        
        # Example findings for all types
        test_dep_conf = [
            {
                'file': 'package.json',
                'package': 'internal-pkg-test',
                'reason': 'Package not found in public registry'
            },
            {
                'file': 'requirements.txt',
                'package': 'my-test-utils',
                'reason': 'Package not found in public registry'
            }
        ]
        
        test_public_not_found = [
            {
                'file': 'package.json',
                'package': 'missing-public-pkg',
                'reason': 'Package not found in public registry'
            },
            {
                'file': 'pyproject.toml',
                'package': 'missing-pypi-pkg',
                'reason': 'Package not found in public registry'
            }
        ]

        test_typosquatting = [
            {
                'file': 'requirements.txt',
                'package': 'reqeusts',
                'similar_to': ['requests']
            },
            {
                'file': 'package.json',
                'package': 'exrpess',
                'similar_to': ['express']
            }
        ]

        logger.info(f"🧪 Running test notification sequence...")
        if not self.is_notification_enabled():
            logger.warning("⚠️ No notification platforms enabled in config.")
            return False

        # Test all finding types
        for finding_type, test_data in [
            ('dependency_confusion', test_dep_conf),
            ('public_not_found', test_public_not_found),
            ('typosquatting', test_typosquatting)
        ]:
            type_label = {
                'dependency_confusion': "Dependency Confusion",
                'public_not_found': "Public Packages Not Found",
                'typosquatting': "Typosquatting"
            }[finding_type]
            
            logger.info(f"Testing {type_label} notifications...")
            
            # Test each enabled platform
            for platform, enabled, test_func in [
                ('Telegram', self.telegram_enabled, lambda: self.send_telegram_message(
                    f"🧪 Test Notification - {type_label} (depconf-checker) 🧪\n\n" +
                    self.format_findings_telegram(test_repo_info, test_data, finding_type)
                )),
                ('Discord', self.discord_enabled, lambda: self.send_discord_message(
                    f"🧪 Test Notification - {type_label} (depconf-checker) 🧪",
                    [self.format_findings_discord(test_repo_info, test_data, finding_type)[1]]
                )),
                ('Slack', self.slack_enabled, lambda: self.send_slack_message(
                    self.format_findings_slack(test_repo_info, test_data, finding_type)
                )),
                ('Teams', self.teams_enabled, lambda: self.send_teams_message(
                    self.format_findings_teams(test_repo_info, test_data, finding_type)
                )),
                ('Email', self.email_enabled, lambda: self.send_email_message(
                    *self.format_findings_email(test_repo_info, test_data, finding_type)
                ))
            ]:
                if enabled:
                    platforms_tested += 1
                    logger.info(f"  -> Attempting {platform} test for {type_label}...")
                    try:
                        if await test_func():
                            logger.info(f"  ✅ {platform} test for {type_label} sent.")
                            success_count += 1
                        else:
                            logger.error(f"  ❌ {platform} test for {type_label} failed.")
                    except Exception as e:
                        logger.error(f"  💥 Error during {platform} test for {type_label}: {e}", exc_info=True)

        # Final result
        if success_count == platforms_tested:
            logger.info(f"✅ All ({success_count}) enabled platforms tested successfully.")
            return True
        else:
            logger.error(f"❌ Test notifications failed for {platforms_tested - success_count} of {platforms_tested} enabled platform(s).")
            return False

    def format_cycle_summary_telegram(self, cycle_stats: Dict[str, Any]) -> str:
        """Formats cycle summary for Telegram."""
        lines = [
            "🔄 *Cycle Summary Report*",
            "",
            f"⏱️ *Duration:* {cycle_stats['duration']:.1f}s",
            f"🔍 *Repos Checked:* {cycle_stats['repos_processed']}",
            f"🐛 *Findings:* {cycle_stats['findings_count']}"
        ]
        
        if cycle_stats.get('errors'):
            lines.append(f"❗ *Errors:* {len(cycle_stats['errors'])}")
            lines.append("")
            lines.append("*Error Details:*")
            for error in cycle_stats['errors'][:5]:  # Show first 5 errors
                lines.append(f"• {error}")
            if len(cycle_stats['errors']) > 5:
                lines.append(f"_... and {len(cycle_stats['errors']) - 5} more errors_")
        
        return "\n".join(lines)

    def format_cycle_summary_discord(self, cycle_stats: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """Formats cycle summary for Discord."""
        content = "🔄 **Cycle Summary Report**"
        
        embed = {
            'title': "Dependency Confusion Scan Results",
            'color': 0x3498DB,  # Blue
            'fields': [
                {
                    'name': "⏱️ Duration",
                    'value': f"{cycle_stats['duration']:.1f}s",
                    'inline': True
                },
                {
                    'name': "🔍 Repos Checked",
                    'value': str(cycle_stats['repos_processed']),
                    'inline': True
                },
                {
                    'name': "🐛 Findings",
                    'value': str(cycle_stats['findings_count']),
                    'inline': True
                }
            ],
            'timestamp': time.strftime('%Y-%m-%dT%H:%M:%SZ', time.gmtime()),
            'footer': {'text': f'depconf-checker v{__version__}'}
        }
        
        if cycle_stats.get('errors'):
            error_text = "\n".join(f"• {error}" for error in cycle_stats['errors'][:5])
            if len(cycle_stats['errors']) > 5:
                error_text += f"\n... and {len(cycle_stats['errors']) - 5} more errors"
            
            embed['fields'].append({
                'name': "❗ Errors",
                'value': error_text,
                'inline': False
            })
        
        return content, embed

    def format_cycle_summary_slack(self, cycle_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Formats cycle summary for Slack."""
        blocks = [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": "🔄 Cycle Summary Report"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*⏱️ Duration:*\n{cycle_stats['duration']:.1f}s"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*🔍 Repos Checked:*\n{cycle_stats['repos_processed']}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*🐛 Findings:*\n{cycle_stats['findings_count']}"
                    }
                ]
            }
        ]
        
        if cycle_stats.get('errors'):
            error_text = "\n".join(f"• {error}" for error in cycle_stats['errors'][:5])
            if len(cycle_stats['errors']) > 5:
                error_text += f"\n... and {len(cycle_stats['errors']) - 5} more errors"
            
            blocks.append({
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*❗ Errors:*\n{error_text}"
                }
            })
        
        return {
            "blocks": blocks,
            "text": "Cycle Summary Report"  # Fallback text
        }

    def format_cycle_summary_teams(self, cycle_stats: Dict[str, Any]) -> Dict[str, Any]:
        """Formats cycle summary for Teams."""
        facts = [
            {
                "name": "⏱️ Duration",
                "value": f"{cycle_stats['duration']:.1f}s"
            },
            {
                "name": "🔍 Repos Checked",
                "value": str(cycle_stats['repos_processed'])
            },
            {
                "name": "🐛 Findings",
                "value": str(cycle_stats['findings_count'])
            }
        ]
        
        body = []
        if cycle_stats.get('errors'):
            error_text = "\n".join(f"• {error}" for error in cycle_stats['errors'][:5])
            if len(cycle_stats['errors']) > 5:
                error_text += f"\n... and {len(cycle_stats['errors']) - 5} more errors"
            
            facts.append({
                "name": "❗ Errors",
                "value": str(len(cycle_stats['errors']))
            })
            
            body.append({
                "type": "TextBlock",
                "text": error_text,
                "wrap": True
            })
        
        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.0",
                        "body": [
                            {
                                "type": "Container",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "🔄 Cycle Summary Report",
                                        "weight": "Bolder",
                                        "size": "Large"
                                    },
                                    {
                                        "type": "FactSet",
                                        "facts": facts
                                    }
                                ]
                            },
                            *body
                        ],
                        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json"
                    }
                }
            ]
        }

    def format_cycle_summary_email(self, cycle_stats: Dict[str, Any]) -> Tuple[str, str, str]:
        """Formats cycle summary for email."""
        subject = "🔄 Dependency Confusion Scan - Cycle Summary"
        
        # HTML body
        html_parts = [
            "<h1>🔄 Cycle Summary Report</h1>",
            "<table style='border-collapse: collapse; width: 100%;'>",
            "<tr><td style='padding: 8px;'><strong>⏱️ Duration:</strong></td><td style='padding: 8px;'>{:.1f}s</td></tr>".format(cycle_stats['duration']),
            "<tr><td style='padding: 8px;'><strong>🔍 Repos Checked:</strong></td><td style='padding: 8px;'>{}</td></tr>".format(cycle_stats['repos_processed']),
            "<tr><td style='padding: 8px;'><strong>🐛 Findings:</strong></td><td style='padding: 8px;'>{}</td></tr>".format(cycle_stats['findings_count'])
        ]
        
        if cycle_stats.get('errors'):
            html_parts.append("<tr><td style='padding: 8px;'><strong>❗ Errors:</strong></td><td style='padding: 8px;'>{}</td></tr>".format(len(cycle_stats['errors'])))
            html_parts.append("</table>")
            html_parts.append("<h2>Error Details:</h2>")
            html_parts.append("<ul>")
            for error in cycle_stats['errors'][:5]:
                html_parts.append(f"<li>{error}</li>")
            if len(cycle_stats['errors']) > 5:
                html_parts.append(f"<li>... and {len(cycle_stats['errors']) - 5} more errors</li>")
            html_parts.append("</ul>")
        else:
            html_parts.append("</table>")
        
        html_body = "\n".join(html_parts)
        
        # Plain text body
        text_parts = [
            "🔄 Cycle Summary Report",
            "",
            f"⏱️ Duration: {cycle_stats['duration']:.1f}s",
            f"🔍 Repos Checked: {cycle_stats['repos_processed']}",
            f"🐛 Findings: {cycle_stats['findings_count']}"
        ]
        
        if cycle_stats.get('errors'):
            text_parts.append(f"❗ Errors: {len(cycle_stats['errors'])}")
            text_parts.append("")
            text_parts.append("Error Details:")
            for error in cycle_stats['errors'][:5]:
                text_parts.append(f"• {error}")
            if len(cycle_stats['errors']) > 5:
                text_parts.append(f"... and {len(cycle_stats['errors']) - 5} more errors")
        
        text_body = "\n".join(text_parts)
        
        return subject, html_body, text_body

    async def send_cycle_report(self, cycle_stats: Dict[str, Any]) -> None:
        """Sends a cycle summary report to all enabled notification platforms.
        
        Args:
            cycle_stats: Dictionary containing cycle statistics
        """
        if not self.is_notification_enabled():
            return
            
        logger.info("📊 Sending cycle summary report...")
        sent_platform = False

        # Telegram
        if self.telegram_enabled:
            try:
                message = self.format_cycle_summary_telegram(cycle_stats)
                if message:
                    await self.send_telegram_message(message)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Telegram cycle report: {e}", exc_info=True)

        # Discord
        if self.discord_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                content, embed = self.format_cycle_summary_discord(cycle_stats)
                if content or embed:
                    await self.send_discord_message(content, [embed] if embed else None)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Discord cycle report: {e}", exc_info=True)

        # Slack
        if self.slack_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                payload = self.format_cycle_summary_slack(cycle_stats)
                if payload:
                    await self.send_slack_message(payload)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Slack cycle report: {e}", exc_info=True)

        # Teams
        if self.teams_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                payload = self.format_cycle_summary_teams(cycle_stats)
                if payload:
                    await self.send_teams_message(payload)
                    sent_platform = True
            except Exception as e:
                logger.error(f"Failed formatting/sending Teams cycle report: {e}", exc_info=True)

        # Email
        if self.email_enabled:
            if sent_platform: await asyncio.sleep(1.1)
            try:
                subject, html_body, text_body = self.format_cycle_summary_email(cycle_stats)
                if subject and (html_body or text_body):
                    await self.send_email_message(subject, html_body, text_body)
            except Exception as e:
                logger.error(f"Failed formatting/sending email cycle report: {e}", exc_info=True)