#!/usr/bin/env python3
"""
Test suite for security fixes in DepConf Checker.
"""

import os
import tempfile
import unittest
from unittest.mock import patch, MagicMock
import yaml

# Import the modules to test
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from depconf.config import ConfigManager


class TestSecurityFixes(unittest.TestCase):
    """Test security-related fixes and improvements."""

    def setUp(self):
        """Set up test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, 'test_config.yaml')

    def tearDown(self):
        """Clean up test environment."""
        if os.path.exists(self.config_file):
            os.remove(self.config_file)
        os.rmdir(self.temp_dir)

    def test_environment_variable_loading(self):
        """Test that environment variables are properly loaded."""
        test_env_vars = {
            'GITHUB_TOKEN': 'test_github_token',
            'GITLAB_TOKEN': 'test_gitlab_token',
            'DISCORD_WEBHOOK_URL': 'https://discord.com/api/webhooks/test',
            'SLACK_WEBHOOK_URL': 'https://hooks.slack.com/test',
            'TEAMS_WEBHOOK_URL': 'https://outlook.office.com/webhook/test',
            'TELEGRAM_BOT_TOKEN': 'test_telegram_token',
            'TELEGRAM_CHAT_ID': 'test_chat_id',
            'SMTP_SERVER': 'smtp.example.com',
            'SMTP_USERNAME': '<EMAIL>',
            'SMTP_PASSWORD': 'test_password',
            'SMTP_FROM_ADDRESS': '<EMAIL>',
            'SMTP_TO_ADDRESSES': '<EMAIL>,<EMAIL>'
        }

        with patch.dict(os.environ, test_env_vars):
            config_manager = ConfigManager()
            config = config_manager.get_config()

            # Test GitHub token
            self.assertIn('test_github_token', config['github']['tokens'])

            # Test GitLab token
            self.assertEqual(config['gitlab']['token'], 'test_gitlab_token')

            # Test notification webhooks
            self.assertEqual(config['notifications']['discord']['webhook_url'], 
                           'https://discord.com/api/webhooks/test')
            self.assertEqual(config['notifications']['slack']['webhook_url'], 
                           'https://hooks.slack.com/test')
            self.assertEqual(config['notifications']['teams']['webhook_url'], 
                           'https://outlook.office.com/webhook/test')

            # Test Telegram
            self.assertEqual(config['notifications']['telegram']['bot_token'], 
                           'test_telegram_token')
            self.assertEqual(config['notifications']['telegram']['chat_id'], 
                           'test_chat_id')

            # Test email settings
            email_cfg = config['notifications']['email']
            self.assertEqual(email_cfg['smtp_server'], 'smtp.example.com')
            self.assertEqual(email_cfg['username'], '<EMAIL>')
            self.assertEqual(email_cfg['password'], 'test_password')
            self.assertEqual(email_cfg['from_addr'], '<EMAIL>')
            self.assertEqual(email_cfg['to_addrs'], ['<EMAIL>', '<EMAIL>'])

    def test_security_validation_warnings(self):
        """Test that security validation properly warns about hardcoded credentials."""
        # Create a config with hardcoded credentials
        insecure_config = {
            'github': {
                'tokens': ['ghp_hardcoded_token_example_12345678901234567890']
            },
            'gitlab': {
                'token': 'hardcoded_gitlab_token'
            },
            'notifications': {
                'discord': {
                    'webhook_url': 'https://discord.com/api/webhooks/123/secret'
                },
                'email': {
                    'password': 'hardcoded_password'
                }
            }
        }

        with open(self.config_file, 'w') as f:
            yaml.dump(insecure_config, f)

        with patch('depconf.config.logger') as mock_logger:
            config_manager = ConfigManager(self.config_file)
            
            # Check that warnings were logged
            warning_calls = [call for call in mock_logger.warning.call_args_list 
                           if 'hardcoded' in str(call) or 'webhook' in str(call)]
            self.assertGreater(len(warning_calls), 0, "Security warnings should be logged")

    def test_secure_config_template(self):
        """Test that the secure config template doesn't contain sensitive data."""
        example_config_path = os.path.join(os.path.dirname(__file__), '..', 'depconf_config.yaml.example')
        
        if os.path.exists(example_config_path):
            with open(example_config_path, 'r') as f:
                content = f.read()
            
            # Check that no actual tokens or webhooks are present
            self.assertNotIn('ghp_', content, "Example config should not contain GitHub tokens")
            self.assertNotIn('discord.com/api/webhooks/', content, "Example config should not contain Discord webhooks")
            self.assertNotIn('hooks.slack.com', content, "Example config should not contain Slack webhooks")

    def test_missing_dependencies_handling(self):
        """Test that missing optional dependencies are handled gracefully."""
        # This test would need to mock the import failures
        # For now, we'll just ensure the modules can be imported without crashing
        try:
            from depconf.checker import DependencyChecker, HAVE_REQUESTS, HAVE_AIOHTTP
            from depconf.repo_identifier import RepositoryIdentifier, GITHUB_AVAILABLE, GITLAB_AVAILABLE
            
            # These should be boolean values indicating availability
            self.assertIsInstance(HAVE_REQUESTS, bool)
            self.assertIsInstance(HAVE_AIOHTTP, bool)
            self.assertIsInstance(GITHUB_AVAILABLE, bool)
            self.assertIsInstance(GITLAB_AVAILABLE, bool)
            
        except ImportError as e:
            self.fail(f"Module import failed: {e}")

    def test_config_validation(self):
        """Test that configuration validation works properly."""
        # Test with invalid configuration
        invalid_config = {
            'general': {
                'log_level': 'INVALID_LEVEL',
                'output_dir': ''
            },
            'dependency_confusion': {
                'http_timeout': -1,
                'http_retries': 10,
                'typosquatting_threshold': 0
            },
            'operation': {
                'concurrency': -1,
                'scan_interval': 0
            }
        }

        with open(self.config_file, 'w') as f:
            yaml.dump(invalid_config, f)

        config_manager = ConfigManager(self.config_file)
        config = config_manager.get_config()

        # Check that invalid values were corrected
        self.assertEqual(config['general']['log_level'], 'info')
        self.assertNotEqual(config['general']['output_dir'], '')
        self.assertEqual(config['dependency_confusion']['http_timeout'], 20)
        self.assertEqual(config['dependency_confusion']['http_retries'], 2)
        self.assertEqual(config['dependency_confusion']['typosquatting_threshold'], 2)
        self.assertEqual(config['operation']['concurrency'], 2)
        self.assertEqual(config['operation']['scan_interval'], 3600)

    def test_proxy_validation(self):
        """Test proxy configuration validation."""
        config_manager = ConfigManager()
        
        # Test valid proxies
        valid_proxies = [
            'http://proxy.example.com:8080',
            'https://secure-proxy.example.com:8080',
            'socks4://socks-proxy.example.com:1080',
            'socks5://socks5-proxy.example.com:1080'
        ]
        
        validated = config_manager._validate_proxies(valid_proxies)
        self.assertEqual(len(validated), 4)
        
        # Test invalid proxies
        invalid_proxies = [
            'ftp://invalid-proxy.example.com:21',
            'not-a-url',
            'proxy.example.com:8080',  # Missing protocol
            123  # Not a string
        ]
        
        validated = config_manager._validate_proxies(invalid_proxies)
        self.assertEqual(len(validated), 0)

    def test_email_validation(self):
        """Test email address validation."""
        config_manager = ConfigManager()
        
        # Test valid email addresses
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        validated = config_manager._validate_email_addresses(valid_emails)
        self.assertEqual(len(validated), 3)
        
        # Test invalid email addresses
        invalid_emails = [
            'not-an-email',
            '@domain.com',
            'user@',
            'user@domain',
            123  # Not a string
        ]
        
        validated = config_manager._validate_email_addresses(invalid_emails)
        self.assertEqual(len(validated), 0)


class TestSecurityDocumentation(unittest.TestCase):
    """Test that security documentation exists and is comprehensive."""

    def test_security_documentation_exists(self):
        """Test that security documentation file exists."""
        security_doc_path = os.path.join(os.path.dirname(__file__), '..', 'docs', 'SECURITY.md')
        self.assertTrue(os.path.exists(security_doc_path), "Security documentation should exist")

    def test_security_documentation_content(self):
        """Test that security documentation covers key topics."""
        security_doc_path = os.path.join(os.path.dirname(__file__), '..', 'docs', 'SECURITY.md')
        
        if os.path.exists(security_doc_path):
            with open(security_doc_path, 'r') as f:
                content = f.read()
            
            # Check for key security topics
            required_topics = [
                'environment variables',
                'API tokens',
                'webhook',
                'credentials',
                'security best practices',
                'deployment security'
            ]
            
            for topic in required_topics:
                self.assertIn(topic.lower(), content.lower(), 
                            f"Security documentation should cover {topic}")


if __name__ == '__main__':
    unittest.main()
