# Security Guidelines for DepConf Checker

## 🔐 Overview

This document outlines security best practices for using and deploying the DepConf Checker tool.

## 🚨 Critical Security Issues Fixed

### 1. Exposed API Tokens
- **Issue**: GitHub tokens and Discord webhook URLs were hardcoded in configuration files
- **Fix**: All sensitive credentials must now be set via environment variables
- **Impact**: Prevents accidental exposure of API tokens in version control

### 2. Configuration Security
- **Issue**: Sensitive data in configuration files
- **Fix**: Created secure configuration template with environment variable placeholders
- **Impact**: Eliminates risk of credential leakage

## 🛡️ Security Best Practices

### Environment Variables
Always use environment variables for sensitive data:

```bash
# GitHub Authentication
export GITHUB_TOKEN="your-github-token"
export GITHUB_TOKENS="token1,token2,token3"  # Multiple tokens for rate limiting

# GitLab Authentication
export GITLAB_TOKEN="your-gitlab-token"

# Notification Services
export DISCORD_WEBHOOK_URL="your-discord-webhook"
export SLACK_WEBHOOK_URL="your-slack-webhook"
export TEAMS_WEBHOOK_URL="your-teams-webhook"
export TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
export TELEGRAM_CHAT_ID="your-telegram-chat-id"

# Email Configuration
export SMTP_SERVER="smtp.example.com"
export SMTP_USERNAME="your-smtp-username"
export SMTP_PASSWORD="your-smtp-password"
export SMTP_FROM_ADDRESS="<EMAIL>"
export SMTP_TO_ADDRESSES="<EMAIL>,<EMAIL>"
```

### Configuration File Security
1. **Never commit sensitive data** to version control
2. **Use the example configuration** as a template
3. **Set appropriate file permissions** (600 or 640)
4. **Regularly rotate API tokens** and webhook URLs

### Network Security
1. **Use HTTPS** for all API communications
2. **Configure proxy settings** if required by your network
3. **Enable SSL verification** for async HTTP requests
4. **Implement rate limiting** to avoid API abuse

### Access Control
1. **Limit GitHub token permissions** to minimum required scopes
2. **Use organization-specific tokens** when possible
3. **Monitor token usage** and revoke unused tokens
4. **Implement webhook secret validation** where supported

## 🔍 Security Monitoring

### Logging
- Enable appropriate log levels for security monitoring
- Monitor for authentication failures
- Track API rate limit violations
- Log all configuration changes

### Alerting
- Set up notifications for security events
- Monitor for unusual scanning patterns
- Alert on configuration errors
- Track dependency confusion findings

## 🚀 Deployment Security

### Production Deployment
1. **Use dedicated service accounts** for API access
2. **Implement secrets management** (e.g., HashiCorp Vault, AWS Secrets Manager)
3. **Enable audit logging** for all operations
4. **Use container security scanning** if deploying in containers
5. **Implement network segmentation** where appropriate

### Container Security
```dockerfile
# Use non-root user
RUN adduser --disabled-password --gecos '' depconf
USER depconf

# Set secure file permissions
COPY --chown=depconf:depconf . /app
RUN chmod 600 /app/depconf_config.yaml
```

### Kubernetes Security
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: depconf-secrets
type: Opaque
stringData:
  github-token: "your-github-token"
  discord-webhook: "your-discord-webhook"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: depconf
spec:
  template:
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: depconf
        env:
        - name: GITHUB_TOKEN
          valueFrom:
            secretKeyRef:
              name: depconf-secrets
              key: github-token
```

## 🔧 Security Configuration

### Recommended Settings
```yaml
dependency_confusion:
  # Enable SSL verification
  async_ssl_verify: true
  
  # Limit concurrent requests to avoid rate limiting
  max_concurrent_requests: 5
  connection_limit: 50
  
  # Add delays to be respectful to APIs
  request_delay: 1
  
  # Enable rate limit handling
  rate_limit_handling:
    enabled: true
    retry_count: 3
    retry_delay: 60
    backoff_factor: 2
    max_retry_delay: 3600
```

## 📋 Security Checklist

- [ ] All API tokens set via environment variables
- [ ] Configuration files contain no sensitive data
- [ ] File permissions set to 600 or 640
- [ ] SSL verification enabled for all HTTP requests
- [ ] Rate limiting configured appropriately
- [ ] Logging enabled for security monitoring
- [ ] Webhook secrets configured where supported
- [ ] Regular token rotation schedule established
- [ ] Network security controls in place
- [ ] Container/deployment security implemented

## 🚨 Incident Response

### If Credentials Are Compromised
1. **Immediately revoke** the compromised tokens/webhooks
2. **Generate new credentials** with appropriate permissions
3. **Update environment variables** with new credentials
4. **Review logs** for any unauthorized access
5. **Notify relevant teams** of the security incident

### Reporting Security Issues
Please report security vulnerabilities to the project maintainers privately before public disclosure.

## 📚 Additional Resources

- [GitHub Token Security](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure)
- [GitLab Token Security](https://docs.gitlab.com/ee/user/profile/personal_access_tokens.html)
- [Discord Webhook Security](https://discord.com/developers/docs/resources/webhook)
- [OWASP Security Guidelines](https://owasp.org/www-project-top-ten/)
