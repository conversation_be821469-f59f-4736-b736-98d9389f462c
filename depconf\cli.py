# depconf/cli.py
# Standard library
import os
import logging
import time
import json
import asyncio
from datetime import datetime
from functools import wraps
from typing import List, Dict, Any, Tuple, Optional

# Third-party
import click
import colorama
from colorama import Fore, Style

# Initialize colorama with autoreset
colorama.init(autoreset=True)

# —————— configure colorized logging before importing our modules ——————
class ColorFormatter(logging.Formatter):
    """Custom formatter that adds colors to log levels and messages."""
    # Map log levels to colors
    LEVEL_COLORS = {
        'DEBUG':    Fore.CYAN,
        'INFO':     Fore.GREEN,
        'WARNING':  Fore.YELLOW,
        'ERROR':    Fore.RED,
        'CRITICAL': Fore.RED + Style.BRIGHT,
    }

    def format(self, record):
        # Get the original message
        message = record.getMessage()
        
        # Apply color to the level name
        levelname = record.levelname
        color = self.LEVEL_COLORS.get(levelname, '')
        record.levelname = f"{color}{levelname}{Style.RESET_ALL}"
        
        # Apply color to the message
        record.message = f"{color}{message}{Style.RESET_ALL}"
        
        # Format the record
        formatted = super().format(record)
        
        # Ensure we don't have double color resets
        return formatted.replace(f"{Style.RESET_ALL}{Style.RESET_ALL}", Style.RESET_ALL)

def setup_logging(log_level: str = 'INFO'):
    """Configure logging with colors."""
    # Remove any existing handlers
    root = logging.getLogger()
    for handler in root.handlers[:]:
        root.removeHandler(handler)
    
    # Create and configure the handler
    handler = logging.StreamHandler()
    handler.setFormatter(ColorFormatter(
        "%(asctime)s %(levelname)s: %(message)s",
        datefmt="%H:%M:%S"
    ))
    
    # Set the log level
    level = getattr(logging, log_level.upper(), logging.INFO)
    root.setLevel(level)
    
    # Add the handler
    root.addHandler(handler)

# Configure root logger with our formatter
setup_logging()

# Local modules
from . import __version__
from .config import ConfigManager
from .notifications import NotificationManager
from .checker import DependencyChecker
from .repo_identifier import RepositoryIdentifier, ScanUncertainException
from . import state # Import the state module

# Note: scan_single_repo, _get_repositories_to_check, and _process_findings
# are defined below in this file, so no external import is needed.

# Module-level logger
logger = logging.getLogger(__name__)

# --- Click CLI Definition ---
@click.group(context_settings=dict(help_option_names=['-h', '--help']))
@click.version_option(__version__, package_name='depconf-checker')
def cli():
    """Checks organization repositories for dependency confusion and related issues. Includes one-off scans and continuous monitoring."""
    pass

# Add async support to Click
def async_command(f):
    """Decorator to add async support to Click commands."""
    @wraps(f)
    def wrapper(*args, **kwargs):
        return asyncio.run(f(*args, **kwargs))
    return wrapper

@cli.command()
@click.option('-c','--config', 'config_path', default='depconf_config.yaml', type=click.Path(dir_okay=False, resolve_path=True), show_default=True, help='Configuration file path.')
@click.option('-o','--org', 'org_targets', multiple=True, help='Specific organization(s)/group(s) to check (overrides config).')
@click.option('-r','--repo', 'repo_targets', multiple=True, help='Specific repository URLs to check. Bypasses org lookup.')
@click.option('--output', help='Output directory for results (overrides config).')
@click.option('--log-level', type=click.Choice(['debug', 'info', 'warning', 'error', 'critical'], case_sensitive=False), help='Log level (overrides config).')
@click.option('--notify/--no-notify', 'use_notifications', default=None, help='Enable/disable notifications (overrides config).')
@click.option('--format', 'output_format', type=click.Choice(['json', 'markdown', 'csv', 'xml'], case_sensitive=False), default='json', help='Output format for findings.')
@click.option('--severity', type=click.Choice(['low', 'medium', 'high', 'critical'], case_sensitive=False), help='Filter findings by minimum severity level.')
@click.option('--exclude-ecosystem', multiple=True, help='Exclude specific ecosystems from scanning.')
@click.option('--include-ecosystem', multiple=True, help='Only scan specific ecosystems.')
@click.option('--dry-run', is_flag=True, help='Perform a dry run without making actual API calls.')
@click.option('--max-repos', type=int, help='Maximum number of repositories to scan.')
@click.option('--parallel', type=int, help='Number of parallel scanning threads.')
@click.pass_context
@async_command
async def check(
    ctx: click.Context,
    config_path: str,
    org_targets: Tuple[str],
    repo_targets: Tuple[str],
    output: Optional[str],
    log_level: Optional[str],
    use_notifications: Optional[bool],
    output_format: str,
    severity: Optional[str],
    exclude_ecosystem: Tuple[str],
    include_ecosystem: Tuple[str],
    dry_run: bool,
    max_repos: Optional[int],
    parallel: Optional[int],
) -> None:
    """Run a dependency confusion check on one or more repositories."""
    checker = None
    notifier = None
    repo_identifier = None # Initialize for broader scope, though it's part of checker now
    
    try:
        # Load configuration
        mgr = ConfigManager(config_path)
        cfg = mgr.get_config()
        if not cfg:
            logger.error("Failed to load configuration")
            ctx.exit(1)

        # Was output_dir in the config?
        had_output = 'output_dir' in cfg

        # Ensure we always have an output_dir (fall back to cwd)
        cfg.setdefault('output_dir', os.getcwd())

        # If it wasn't, log that we're defaulting
        if not had_output:
            logger.info(f"No output_dir in config; defaulting to {cfg['output_dir']}")

        # Apply overrides
        if output:
            cfg['output_dir'] = output
        if log_level:
            logging.getLogger().setLevel(log_level.upper())
        if use_notifications is not None:
            cfg.setdefault('dependency_confusion', {}) \
               .setdefault('notifications', {})['enabled'] = use_notifications

        # Initialize components
        checker, repo_identifier, notifier = await _initialize_components(cfg)
        if not all([checker, repo_identifier]):
            logger.error("Failed to initialize required components")
            ctx.exit(1)

        try:
            cycle_start = time.time()
            cycle_stats = {
                'start_time': cycle_start,
                'repos_processed': 0,
                'findings_count': 0,
                'errors': []
            }

            # Single-repo mode
            if repo_targets:
                for repo_name_str in repo_targets:
                    minimal_repo_data = {
                        'full_name': repo_name_str,
                        'platform': 'github',
                        'html_url': f'https://github.com/{repo_name_str}',
                        'organization': repo_name_str.split('/')[0] if '/' in repo_name_str else None
                    }
                    logger.info(f"Attempting scan for {repo_name_str} (assuming GitHub, default branch). Full data retrieval for single repos is pending.")
                    findings = await scan_single_repo(
                        minimal_repo_data,
                        checker,
                        base_sha_for_scan=None,
                        head_sha_for_scan=minimal_repo_data.get('latest_commit_sha'), # Or a suitable default like 'HEAD'
                        package_file_patterns_from_config=cfg.get('operation', {}).get('package_file_patterns', [])
                    )
                    if findings:
                        await _process_findings(findings, cfg['output_dir'], notifier, minimal_repo_data)
                        cycle_stats['findings_count'] += len(findings)
                    cycle_stats['repos_processed'] += 1

                await _process_monitor_cycle_results(cycle_stats, cfg['output_dir'], notifier)
                _print_cycle_summary(cycle_stats)
                return

            # Org mode
            # Get repositories to check, honoring org_targets override
            if org_targets:
                repos = [{'full_name': o} for o in org_targets]
                logger.info(f"Using explicitly specified organizations: {', '.join(org_targets)}")
            else:
                repos = await _get_repositories_to_check(cfg, repo_identifier)
                if not repos:
                    logger.warning("No repositories found to check")
                    return

            # Process each repository
            for repo_data in repos:
                try:
                    findings = await scan_single_repo(
                        repo_data,
                        checker,
                        base_sha_for_scan=None, # For 'check' command, always full scan of head
                        head_sha_for_scan=repo_data.get('latest_commit_sha'), # Or a suitable default
                        package_file_patterns_from_config=cfg.get('operation', {}).get('package_file_patterns', [])
                    )
                    if findings:
                        cycle_stats['findings_count'] += len(findings)
                        await _process_findings(findings, cfg['output_dir'], notifier, repo_data)
                    cycle_stats['repos_processed'] += 1
                except Exception as e:
                    cycle_stats['errors'].append(f"Error processing {repo_data['full_name']}: {str(e)}")
                    logger.error(f"Error processing {repo_data['full_name']}: {str(e)}")

            # Process cycle results
            await _process_monitor_cycle_results(cycle_stats, cfg['output_dir'], notifier)
            _print_cycle_summary(cycle_stats)

        except Exception as e:
            logger.error(f"Error during check: {str(e)}", exc_info=True)
            ctx.exit(1)
        finally:
            # Cleanup
            if checker:
                await checker.close()
            if notifier:
                await notifier.close()

    except Exception as e:
        logger.error(f"Error during check: {str(e)}", exc_info=True)
        ctx.exit(1)
        # Ensure cleanup happens even if outer try block fails
        if checker:
            await checker.close()
        if notifier:
            await notifier.close()

@cli.command()
@click.option('-c','--config', 'config_path', default='depconf_config.yaml', type=click.Path(dir_okay=False, resolve_path=True), show_default=True, help='Configuration file path.')
@click.option('--output', help='Output directory for results (overrides config).')
@click.option('--log-level', type=click.Choice(['debug', 'info', 'warning', 'error', 'critical'], case_sensitive=False), help='Log level (overrides config).')
@click.option('--notify/--no-notify', 'use_notifications', default=None, help='Enable/disable notifications (overrides config).')
@click.option('--scan-interval', type=int, help='Override scan interval from config (seconds).')
@click.option('--run-once', is_flag=True, default=False, help='Run the monitor logic once and exit (for testing).')
@click.pass_context
@async_command
async def monitor(
    ctx: click.Context,
    config_path: str,
    output: Optional[str],
    log_level: Optional[str],
    use_notifications: Optional[bool],
    scan_interval: Optional[int],
    run_once: bool,
) -> None:
    """Monitor repositories for dependency confusion issues."""
    checker = None
    notifier = None
    
    try:
        # Load configuration
        mgr = ConfigManager(config_path)
        cfg = mgr.get_config()
        if not cfg:
            logger.error("Failed to load configuration")
            ctx.exit(1)

        # Was output_dir in the config?
        had_output = 'output_dir' in cfg

        # Ensure we always have an output_dir (fall back to cwd)
        cfg.setdefault('output_dir', os.getcwd())

        # If it wasn't, log that we're defaulting
        if not had_output:
            logger.info(f"No output_dir in config; defaulting to {cfg['output_dir']}")

        # Apply overrides
        if output:
            cfg['output_dir'] = output
        if log_level:
            logging.getLogger().setLevel(log_level.upper())
        if use_notifications is not None:
            cfg.setdefault('dependency_confusion', {}) \
               .setdefault('notifications', {})['enabled'] = use_notifications
        if scan_interval:
            cfg.setdefault('operation', {})['scan_interval'] = scan_interval

        # Initialize components
        checker, repo_identifier, notifier = await _initialize_components(cfg)
        if not all([checker, repo_identifier]):
            logger.error("Failed to initialize required components")
            ctx.exit(1)

        try:
            while True:
                cycle_start = time.time()
                cycle_stats = {
                    'start_time': cycle_start,
                    'repos_processed': 0,
                    'findings_count': 0,
                    'errors': []
                }

                # Get repositories to check (possibly empty)
                repos = await _get_repositories_to_check(cfg, repo_identifier)
                if not repos:
                    logger.warning("No repositories found to check")
                else:
                    # Load current scan states
                    output_directory = cfg.get('general', {}).get('output_dir', '.')
                    repo_scan_states = state.load_repo_scan_state(output_directory)

                    # Process each repository
                    for repo_data in repos: # 'repos' now contains list of repo_data dictionaries
                        cycle_stats['repos_processed'] += 1 # Count repo as processed even if skipped
                        current_repo_sha = repo_data.get('latest_commit_sha')
                        # Ensure platform is part of the key for uniqueness across platforms
                        repo_id_key = f"{repo_data.get('platform', 'unknown_platform')}:{repo_data.get('full_name', 'unknown_repo')}"

                        last_known_state = repo_scan_states.get(repo_id_key)
                        last_known_sha = None
                        if last_known_state and isinstance(last_known_state, dict):
                            last_known_sha = last_known_state.get('last_commit_sha')

                        scan_this_repo = False
                        if not last_known_sha:
                            logger.info(f"Repository {repo_id_key} is new or has no previous scan state. Queuing for full scan (HEAD: {current_repo_sha}).")
                            scan_this_repo = True
                        elif last_known_sha != current_repo_sha:
                            logger.info(f"Repository {repo_id_key} state changed. Old SHA: {last_known_sha}, New SHA: {current_repo_sha}. Queuing for differential scan.")
                            scan_this_repo = True
                        else:
                            logger.info(f"Repository {repo_id_key} unchanged (SHA: {current_repo_sha}). Skipping scan.")

                        if scan_this_repo:
                            try:
                                findings = await scan_single_repo(
                                    repo_data,
                                    checker,
                                    base_sha_for_scan=last_known_sha if last_known_sha != current_repo_sha else None,
                                    head_sha_for_scan=current_repo_sha,
                                    package_file_patterns_from_config=cfg.get('operation', {}).get('package_file_patterns', [])
                                )
                                if findings:
                                    cycle_stats['findings_count'] += len(findings)
                                    await _process_findings(findings, output_directory, notifier, repo_data)

                                # Update state only if scan was successful or no findings (but processed)
                                state.update_and_save_repo_state_immediately(
                                    output_directory,
                                    repo_id_key,
                                    current_repo_sha,
                                    int(time.time()),
                                    repo_scan_states # Pass the whole state dict to be updated in place
                                )
                            except Exception as e: # Catch errors from scan_single_repo or _process_findings
                                cycle_stats['errors'].append(f"Error processing {repo_id_key}: {str(e)}")
                                logger.error(f"Error processing {repo_id_key}: {str(e)}", exc_info=True)
                        # No else needed, repos_processed is incremented at loop start

                # Process cycle results (even if repos was empty)
                await _process_monitor_cycle_results(cycle_stats, cfg['output_dir'], notifier)
                _print_cycle_summary(cycle_stats)

                if run_once:
                    break

                # Wait for next cycle
                interval = cfg.get('operation', {}).get('scan_interval', 3600)
                elapsed = time.time() - cycle_start
                to_sleep = max(0, interval - elapsed)
                logger.info(f"Waiting {to_sleep:.1f}s until next cycle")
                await asyncio.sleep(to_sleep)

        except Exception as e:
            logger.error(f"Monitor error: {str(e)}", exc_info=True)
            ctx.exit(1)
        finally:
            # Cleanup
            if checker:
                await checker.close()
            if notifier:
                await notifier.close()

    except Exception as e:
        logger.error(f"Error during monitor: {str(e)}", exc_info=True)
        ctx.exit(1)
        # Ensure cleanup happens even if outer try block fails
        if checker:
            await checker.close()
        if notifier:
            await notifier.close()

#
# CLI-specific helper functions
#
async def _process_monitor_cycle_results(
    cycle_stats: Dict[str, Any],
    output_dir: str,
    notifier: Optional[NotificationManager]
) -> None:
    """Process and save cycle results, optionally sending notifications.
    
    Args:
        cycle_stats: Dictionary containing cycle statistics
        output_dir: Directory to save results
        notifier: Optional notification manager
    """
    cycle_stats['duration'] = time.time() - cycle_stats['start_time']
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    path = os.path.join(output_dir, f'cycle_results_{timestamp}.json')
    with open(path, 'w') as f:
        json.dump(cycle_stats, f, indent=2)
    logger.info(f"Wrote cycle results to {path}")

    if notifier and (cycle_stats['findings_count'] or cycle_stats['errors']):
        try:
            await notifier.send_cycle_report(cycle_stats)
        except Exception:
            logger.exception("Failed to send cycle summary notification")

async def _initialize_components(cfg: Dict[str, Any]) -> Tuple[Optional[DependencyChecker], Optional[RepositoryIdentifier], Optional[NotificationManager]]:
    """Initialize the core components needed for dependency checking.
    
    Args:
        cfg: Configuration dictionary
        
    Returns:
        Tuple of (checker, repo_identifier, notifier) instances
    """
    try:
        # Initialize repository identifier first, as it's needed by the checker
        # The RepositoryIdentifier constructor is already set up to take cfg and extract what it needs.
        repo_identifier = RepositoryIdentifier(cfg=cfg)

        # Initialize dependency checker, passing the repo_identifier
        checker = DependencyChecker(cfg.get('dependency_confusion', {}), repo_identifier=repo_identifier)

        # Initialize HTTP session for the checker
        # Pass only the dependency_confusion part of the config to _init_http_session,
        # as _init_http_session in checker.py expects the sub-config.
        await checker._init_http_session()

        if not checker.enabled:
            logger.warning("Dependency checker is disabled in configuration.")
            # Still return repo_identifier if it was created, checker might be disabled but identifier could be useful.
            return None, repo_identifier, None

        # Initialize notification manager if enabled
        notifier = None
        if cfg.get('notifications', {}).get('enabled', False):
            notifier = NotificationManager(cfg.get('notifications', {}))

        return checker, repo_identifier, notifier

    except Exception as e:
        logger.error(f"Failed to initialize components: {str(e)}", exc_info=True)
        # Ensure that if repo_identifier was created before an error, it's part of the return if applicable.
        # However, the original return signature expects specific types or None.
        # If checker init fails after repo_identifier, we might want repo_identifier. For now, stick to None for failed components.
        return None, None, None

def _print_cycle_summary(cycle_stats: Dict[str, Any]) -> None:
    """Print a brief summary to stdout.
    
    Args:
        cycle_stats: Dictionary containing cycle statistics
    """
    duration = cycle_stats.get('duration', 0.0)
    repos = cycle_stats.get('repos_processed', 0)
    findings = cycle_stats.get('findings_count', 0)
    errors = len(cycle_stats.get('errors', []))

    lines = [
        f"✔️  Completed in {duration:.1f}s",
        f"🔍  Repos checked: {repos}",
        f"🐛  Findings found: {findings}"
    ]
    if errors:
        lines.append(f"❗ Errors: {errors}")

    click.echo("\n".join(lines))

async def scan_single_repo(
    repo_data: Dict[str, Any],
    checker: DependencyChecker,
    base_sha_for_scan: Optional[str],
    head_sha_for_scan: Optional[str],
    package_file_patterns_from_config: List[str]
) -> List[Dict[str, Any]]:
    """Scan a single repository for dependency confusion issues.
    
    Args:
        repo_data: Dictionary containing repository details.
        checker: DependencyChecker instance.
        base_sha_for_scan: The base SHA for a differential scan, or None for a full scan.
        head_sha_for_scan: The head SHA to scan at.
        package_file_patterns_from_config: List of package file patterns from config.
        
    Returns:
        List of findings
    """
    repo_name = repo_data.get('full_name')
    platform = repo_data.get('platform')

    # Ensure head_sha_for_scan is not None; it's essential.
    # It should be current_repo_sha passed from monitor or check command.
    if not head_sha_for_scan:
        logger.error(f"Head SHA for scan is missing for {repo_name}. Using latest_commit_sha from repo_data as fallback.")
        head_sha_for_scan = repo_data.get('latest_commit_sha') # Fallback, though caller should provide it

    if not repo_name or not platform or not head_sha_for_scan:
        logger.error(f"Missing critical data for scan: name='{repo_name}', platform='{platform}', head_sha='{head_sha_for_scan}'. Skipping.")
        return []

    scan_type = "differential" if base_sha_for_scan else "full"
    logger.info(f"Starting {scan_type} scan for repository: {repo_name} on {platform} (Base: {base_sha_for_scan or 'N/A'}, Head: {head_sha_for_scan})")

    try:
        package_file_patterns = package_file_patterns_from_config
        repo_identifier_instance = checker.repo_identifier # Correctly access via checker

        try:
            package_files_paths = await asyncio.to_thread(
                repo_identifier_instance.get_changed_package_files, # Corrected: use instance
                repo_full_name=repo_name,
                platform=platform,
                package_file_patterns=package_file_patterns,
                base_sha=base_sha_for_scan,
                head_sha=head_sha_for_scan
            )
        except ScanUncertainException as e:
            logger.warning(f"Could not determine package files for {repo_name} (Base: {base_sha_for_scan}, Head: {head_sha_for_scan}): {e}")
            return []
        except Exception as e:
            logger.error(f"Error listing package files for {repo_name} (Base: {base_sha_for_scan}, Head: {head_sha_for_scan}): {e}", exc_info=True)
            return []
        
        if not package_files_paths:
            logger.info(f"No relevant package files found (or changed) in {repo_name} for the given SHAs/patterns.")
            return []

        logger.info(f"Found {len(package_files_paths)} package file(s) in {repo_name} to check: {package_files_paths}")
            
        findings = await checker.check_repository(
            repo_full_name=repo_name,
            changed_files=package_files_paths,
            platform=platform,
            ref=head_sha_for_scan # Fetch content from this specific ref
        )
        return findings
        
    except Exception as e:
        logger.error(f"Error scanning repository {repo_name}: {e}", exc_info=True)
        return []

async def _process_findings(
    findings: List[Dict[str, Any]],
    output_dir: str,
    notifier: Optional[NotificationManager],
    repo_data: Dict[str, Any]
) -> None:
    """Process and save findings, optionally sending notifications.
    
    Args:
        findings: List of findings to process
        output_dir: Directory to save findings
        notifier: Optional notification manager
        repo_data: Repository information for notifications
    """
    if not findings:
        return

    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Save findings to file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    findings_file = os.path.join(output_dir, f'findings_{timestamp}.json')
    with open(findings_file, 'w') as f:
        json.dump(findings, f, indent=2)
    logger.info(f"Saved {len(findings)} findings to {findings_file}")

    # Group findings by type
    if notifier:
        grouped_findings: Dict[str, List[Dict]] = {}
        for finding in findings:
            ftype = finding.get('type', 'dependency_confusion')  # Default to dependency_confusion if not specified
            if ftype not in grouped_findings:
                grouped_findings[ftype] = []
            grouped_findings[ftype].append(finding)

        # Send notifications for each finding type
        for ftype, f_list in grouped_findings.items():
            if not f_list:
                continue
            
            logger.info(f"Notifying for {len(f_list)} findings of type '{ftype}' from repo {repo_data.get('full_name')}")
            try:
                await notifier.notify_findings(
                    repo_info=repo_data,
                    findings_list=f_list,
                    finding_type=ftype
                )
                # Add a small delay between different finding types
                await asyncio.sleep(0.5)
            except Exception as e:
                logger.error(f"Failed to send notification for type {ftype}: {e}", exc_info=True)

async def _get_repositories_to_check(cfg: Dict[str, Any], repo_identifier: RepositoryIdentifier) -> List[Dict[str, Any]]:
    """Get list of repositories to check based on configuration.
    
    Args:
        cfg: Configuration dictionary
        repo_identifier: RepositoryIdentifier instance
        
    Returns:
        List of repository dictionaries with at least 'full_name' key
    """
    repos = []
    
    # Get GitHub organizations (fall back to top-level `organizations:` if none under `github`)
    top_level_orgs = cfg.get('organizations', [])
    github_orgs = cfg.get('github', {}).get('organizations') or top_level_orgs

    if github_orgs:
        logger.info(f"Found {len(github_orgs)} organizations to check")
        for org_name in github_orgs: # Renamed org to org_name for clarity
            try:
                # get_organization_repos is synchronous
                org_repos_data = await asyncio.to_thread(repo_identifier.get_organization_repos, org_name)
                if org_repos_data:
                    # Ensure platform is correctly set or inferred if needed.
                    # get_organization_repos should already return dicts with 'platform'.
                    repos.extend(org_repos_data)
                    logger.debug(f"Found {len(org_repos_data)} repos in GitHub org {org_name}")
            except Exception as e:
                logger.error(f"Error getting repositories for GitHub org {org_name}: {str(e)}")
    
    # Get GitLab groups (try both locations)
    gitlab_groups = (
        cfg.get('gitlab', {}).get('groups')
        or cfg.get('groups', [])  # Fall back to top-level groups
    )
    if gitlab_groups:
        logger.info(f"Found {len(gitlab_groups)} GitLab groups to check")
        for group_name in gitlab_groups: # Renamed group to group_name for clarity
            try:
                 # get_organization_repos is synchronous
                group_repos_data = await asyncio.to_thread(repo_identifier.get_organization_repos, group_name)
                if group_repos_data:
                    repos.extend(group_repos_data)
                    logger.debug(f"Found {len(group_repos_data)} repos in GitLab group {group_name}")
            except Exception as e:
                logger.error(f"Error getting repositories for GitLab group {group_name}: {str(e)}")
    
    # Remove duplicates (in case a repo exists in both GitHub and GitLab, or listed multiple times)
    # The repo_data from get_organization_repos already contains 'platform'.
    seen_repo_platform_tuples = set()
    unique_repos = []
    for repo_data_item in repos: # iterate over list of dicts
        platform_val = repo_data_item.get('platform', 'unknown')
        # Use a tuple of full_name and platform to define uniqueness
        repo_tuple = (repo_data_item['full_name'], platform_val)
        if repo_tuple not in seen_repo_platform_tuples:
            seen_repo_platform_tuples.add(repo_tuple)
            unique_repos.append(repo_data_item) # Add the dict itself
    
    logger.info(f"Found {len(unique_repos)} unique repositories to check across all specified organizations/groups.")
    return unique_repos

def _format_cycle_summary(stats: Dict[str, Any]) -> str:
    """Format cycle statistics into a human-readable summary.
    
    Args:
        stats: Dictionary containing cycle statistics
        
    Returns:
        Formatted summary string
    """
    lines = [
        f"✔️  Completed in {stats['duration']:.1f}s",
        f"🔍  Repos checked: {stats['repos_processed']}",
        f"🐛  Findings: {stats['findings_count']}"
    ]
    if stats['errors']:
        lines.append(f"❗ Errors: {len(stats['errors'])}")
    return "\n".join(lines)

# Add new CLI commands for advanced features

@cli.command()
@click.option('-c','--config', 'config_path', default='depconf_config.yaml', type=click.Path(dir_okay=False, resolve_path=True), show_default=True, help='Configuration file path.')
@click.option('--package', required=True, help='Package name to analyze.')
@click.option('--ecosystem', required=True, type=click.Choice(['pip', 'npm', 'rubygems', 'maven', 'composer', 'gomodules', 'nuget']), help='Package ecosystem.')
@click.option('--check-variants', is_flag=True, help='Check for typosquatting variants.')
@click.pass_context
@async_command
async def analyze_package(
    ctx: click.Context,
    config_path: str,
    package: str,
    ecosystem: str,
    check_variants: bool
) -> None:
    """Analyze a specific package for dependency confusion vulnerabilities."""
    try:
        # Load configuration
        mgr = ConfigManager(config_path)
        cfg = mgr.get_config()
        if not cfg:
            logger.error("Failed to load configuration")
            ctx.exit(1)

        # Initialize components
        checker, repo_identifier, notifier = await _initialize_components(cfg)
        if not checker:
            logger.error("Failed to initialize dependency checker")
            ctx.exit(1)

        # Create package data
        package_data = {
            'name': package,
            'ecosystem': ecosystem,
            'version': None,
            'source_file': 'manual_analysis',
            'scope': None
        }

        # Perform analysis
        finding = await checker._check_dependency_confusion(package_data)

        if finding:
            click.echo(f"🚨 Vulnerability found for {package}:")
            click.echo(f"   Type: {finding.get('type', 'unknown')}")
            click.echo(f"   Details: {finding.get('details', 'No details available')}")

            if finding.get('type') == 'typosquatting':
                click.echo(f"   Similar to: {finding.get('suspected_typo_of', 'unknown')}")
                click.echo(f"   Distance: {finding.get('levenshtein_distance', 'unknown')}")
        else:
            click.echo(f"✅ No vulnerabilities found for {package}")

        # Check variants if requested
        if check_variants:
            click.echo(f"\n🔍 Checking typosquatting variants for {package}...")
            variants = checker._generate_package_variants(package, ecosystem)

            if variants:
                click.echo(f"Found {len(variants)} potential variants:")
                for variant in variants[:10]:  # Show first 10
                    click.echo(f"   - {variant}")
                if len(variants) > 10:
                    click.echo(f"   ... and {len(variants) - 10} more")
            else:
                click.echo("No variants generated")

        # Cleanup
        if checker:
            await checker.close()

    except Exception as e:
        logger.error(f"Error during package analysis: {str(e)}", exc_info=True)
        ctx.exit(1)

@cli.command()
@click.option('-c','--config', 'config_path', default='depconf_config.yaml', type=click.Path(dir_okay=False, resolve_path=True), show_default=True, help='Configuration file path.')
@click.option('--ecosystem', type=click.Choice(['pip', 'npm', 'rubygems', 'maven', 'composer', 'gomodules', 'nuget']), help='Filter by ecosystem.')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json', 'csv'], case_sensitive=False), default='table', help='Output format.')
@click.pass_context
def list_top_packages(
    ctx: click.Context,
    config_path: str,
    ecosystem: Optional[str],
    output_format: str
) -> None:
    """List top packages used for typosquatting detection."""
    try:
        # Load configuration
        mgr = ConfigManager(config_path)
        cfg = mgr.get_config()
        if not cfg:
            logger.error("Failed to load configuration")
            ctx.exit(1)

        top_packages = cfg.get('dependency_confusion', {}).get('top_packages', {})

        if ecosystem:
            if ecosystem in top_packages:
                packages = {ecosystem: top_packages[ecosystem]}
            else:
                click.echo(f"No top packages configured for ecosystem: {ecosystem}")
                ctx.exit(1)
        else:
            packages = top_packages

        if output_format == 'table':
            for eco, pkg_list in packages.items():
                click.echo(f"\n📦 {eco.upper()} ({len(pkg_list)} packages):")
                for pkg in pkg_list:
                    click.echo(f"   - {pkg}")
        elif output_format == 'json':
            import json
            click.echo(json.dumps(packages, indent=2))
        elif output_format == 'csv':
            for eco, pkg_list in packages.items():
                for pkg in pkg_list:
                    click.echo(f"{eco},{pkg}")

    except Exception as e:
        logger.error(f"Error listing top packages: {str(e)}", exc_info=True)
        ctx.exit(1)

@cli.command()
@click.option('-c','--config', 'config_path', default='depconf_config.yaml', type=click.Path(dir_okay=False, resolve_path=True), show_default=True, help='Configuration file path.')
@click.pass_context
@async_command
async def test_notifications(
    ctx: click.Context,
    config_path: str
) -> None:
    """Test notification configuration by sending test messages."""
    try:
        # Load configuration
        mgr = ConfigManager(config_path)
        cfg = mgr.get_config()
        if not cfg:
            logger.error("Failed to load configuration")
            ctx.exit(1)

        # Initialize notification manager
        notifier = NotificationManager(cfg.get('notifications', {}))

        if not notifier.is_notification_enabled():
            click.echo("❌ No notification channels are enabled in configuration")
            ctx.exit(1)

        click.echo("🧪 Testing notification configuration...")

        # Run test notifications
        success = await notifier.test_notifications()

        if success:
            click.echo("✅ Test notifications sent successfully!")
        else:
            click.echo("❌ Some test notifications failed. Check logs for details.")
            ctx.exit(1)

        # Cleanup
        if notifier:
            await notifier.close()

    except Exception as e:
        logger.error(f"Error testing notifications: {str(e)}", exc_info=True)
        ctx.exit(1)

if __name__ == "__main__":
    logger.info("This INFO log should be green")
    logger.warning("This WARNING log should be yellow")
    logger.error("This ERROR log should be red")