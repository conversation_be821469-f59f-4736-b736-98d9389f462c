# depconf/__init__.py
"""
Package initialization file for depconf checker.
"""


__version__ = '0.3.0' # <-- Updated version

# Expose key components for potential programmatic use
from .config import ConfigManager
from .repo_identifier import RepositoryIdentifier
from .checker import DependencyChecker  # Core checking functionality
from .notifications import NotificationManager
from .state import load_repo_scan_state, save_repo_scan_state
from .cli import cli

# For convenience, expose commonly used components at package level
__all__ = [
    'ConfigManager',
    'RepositoryIdentifier',
    'DependencyChecker',
    'NotificationManager',
    'load_repo_scan_state',
    'save_repo_scan_state',
    'cli',
]
