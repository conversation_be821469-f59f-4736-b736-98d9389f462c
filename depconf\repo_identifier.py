# depconf/repo_identifier.py
import logging
import fnmatch
import time
import functools
import itertools
from typing import List, Dict, Any, Optional
from datetime import datetime

# --- Custom exceptions for centralized error handling ---
class ScanUncertainException(Exception):
    """Raised when we can't determine if a repo needs scanning."""
    pass

# --- Conditional Platform Imports ---
try:
    from github import Github, GithubException, RateLimitExceededException, UnknownObjectException
    GITHUB_AVAILABLE = True
except ImportError:
    GITHUB_AVAILABLE = False

try:
    import gitlab
    GITLAB_AVAILABLE = True
except ImportError:
    GITLAB_AVAILABLE = False

from colorama import Fore, Style

logger = logging.getLogger('depconf.repo-identifier')

def github_rate_limit_aware(max_wait_time: int = 3600):
    """Decorator for GitHub API methods to handle rate limiting."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # Check if we're still in the cool-down period
            if hasattr(self, 'github_rate_limit_resets_at') and self.github_rate_limit_resets_at > 0:
                wait_time = self.github_rate_limit_resets_at - time.time()
                if wait_time > 0:
                    # If we have multiple clients, try switching instead of waiting
                    if hasattr(self, 'github_clients') and len(self.github_clients) > 1:
                        self.logger.debug("Current client in cool-down, attempting to switch clients")
                        new_client = self._get_next_available_github_client()
                        if new_client and new_client is not self.github_client:
                            self.logger.info(f"Switched to a different GitHub client due to rate limit cool-down")
                            self.github_client = new_client
                        else:
                            # No other clients available, follow normal cool-down procedure
                            if not kwargs.get('run_once', False) and wait_time < max_wait_time:
                                self.logger.warning(f"All clients rate limited. Waiting {wait_time:.1f}s before API call.")
                                time.sleep(wait_time + 1)  # Add 1 second buffer
                            else:
                                raise RateLimitExceededException(0, f"All clients rate limited. Reset at {datetime.fromtimestamp(self.github_rate_limit_resets_at)}.")
                    else:
                        # Single client approach
                        if not kwargs.get('run_once', False) and wait_time < max_wait_time:
                            self.logger.warning(f"Rate limit cooling down. Waiting {wait_time:.1f}s before API call.")
                            time.sleep(wait_time + 1)  # Add 1 second buffer
                        else:
                            raise RateLimitExceededException(0, f"Rate limit cooling down. Reset at {datetime.fromtimestamp(self.github_rate_limit_resets_at)}.")
            
            try:
                return func(self, *args, **kwargs)
            except RateLimitExceededException as e:
                # First try to switch to another client if available
                if hasattr(self, 'github_clients') and len(self.github_clients) > 1:
                    self.logger.info("Rate limit hit, attempting to switch GitHub clients")
                    new_client = self._get_next_available_github_client()
                    if new_client and new_client is not self.github_client:
                        self.github_client = new_client
                        self.logger.info("Switched GitHub client due to rate limit, retrying operation")
                        return func(self, *args, **kwargs)  # Retry with new client
                
                # If we couldn't switch or all clients are rate limited, handle normally
                self._handle_github_rate_limit()
                if not kwargs.get('run_once', False) and self.github_rate_limit_resets_at > 0:
                    wait_time = max(0, self.github_rate_limit_resets_at - time.time())
                    if wait_time < max_wait_time:
                        self.logger.warning(f"Rate limit hit. Waiting {wait_time:.1f}s before retry.")
                        time.sleep(wait_time + 5)  # Add 5 seconds buffer
                        return func(self, *args, **kwargs)  # Retry once after waiting
                raise  # Re-raise if we can't/won't wait
        return wrapper
    return decorator

def gitlab_rate_limit_aware(max_wait_time: int = 3600):
    """Decorator for GitLab API methods to handle rate limiting."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            if not self.gitlab_client:
                return func(self, *args, **kwargs)
                
            retry_count = 0
            while retry_count < self.max_retries:
                try:
                    return func(self, *args, **kwargs)
                except gitlab.exceptions.GitlabHttpError as e:
                    # Check if it's a rate limit error (429 status code)
                    if e.response_code == 429:
                        # GitLab rate limit hit
                        reset_time = time.time() + 60  # Default 1 minute if not specified
                        if hasattr(e, 'response') and e.response is not None:
                            reset_header = e.response.headers.get('RateLimit-Reset')
                            if reset_header:
                                try:
                                    reset_time = float(reset_header)
                                except (ValueError, TypeError):
                                    pass
                        
                        wait_time = max(0, reset_time - time.time())
                        if not kwargs.get('run_once', False) and wait_time < max_wait_time:
                            self.logger.warning(f"GitLab rate limit hit. Waiting {wait_time:.1f}s before retry.")
                            time.sleep(wait_time + 1)  # Add 1 second buffer
                            return func(self, *args, **kwargs)  # Retry once after waiting
                        raise  # Re-raise if we can't/won't wait
                    raise  # Re-raise if it's not a rate limit error
                except gitlab.exceptions.GitlabConnectionError as e:
                    # Handle connection errors with exponential backoff
                    if not kwargs.get('run_once', False):
                        backoff = self.initial_backoff * (2 ** retry_count)
                        self.logger.warning(f"GitLab connection error: {e}. Retrying in {backoff:.1f}s... (attempt {retry_count + 1}/{self.max_retries})")
                        time.sleep(backoff)
                        retry_count += 1
                        continue
                    raise
                except gitlab.exceptions.GitlabAuthenticationError as e:
                    # Authentication errors are unlikely to be resolved by retrying
                    self.logger.error(f"GitLab authentication error: {e}")
                    raise
                except gitlab.exceptions.GitlabGetError as e:
                    if e.response_code == 404:
                        # Not found errors are not retryable
                        raise
                    # Other GitLab API errors might be transient
                    if not kwargs.get('run_once', False):
                        backoff = self.initial_backoff * (2 ** retry_count)
                        self.logger.warning(f"GitLab API error: {e}. Retrying in {backoff:.1f}s... (attempt {retry_count + 1}/{self.max_retries})")
                        time.sleep(backoff)
                        retry_count += 1
                        continue
                    raise
            # If we get here, we've exhausted all retries
            raise gitlab.exceptions.GitlabConnectionError("Max retries exceeded for GitLab API call")
        return wrapper
    return decorator

class RepositoryIdentifier:
    """Identifies repositories from GitHub organizations or GitLab groups."""

    def __init__(self, cfg: Optional[Dict[str, Any]] = None, *, 
                 github_token: Optional[str] = None,
                 github_tokens: Optional[List[str]] = None,
                 gitlab_url: Optional[str] = None,
                 gitlab_token: Optional[str] = None,
                 gitlab_enabled: bool = True,
                 max_retries: int = 3,
                 initial_backoff: int = 5):
        """Initialize the repository identifier with configuration.
        
        Args:
            cfg: Full configuration dictionary (optional)
            github_token: Single GitHub token (optional)
            github_tokens: List of GitHub tokens (optional)
            gitlab_url: GitLab API URL (optional)
            gitlab_token: GitLab token (optional)
            gitlab_enabled: Whether GitLab support is enabled
            max_retries: Maximum number of retries for API calls
            initial_backoff: Initial backoff time in seconds
        """
        self.logger = logging.getLogger('depconf.repo-identifier')
        
        # If full config is provided, use it
        if cfg is not None:
            self.cfg = cfg
            self.dc_config = cfg.get('dependency_confusion', {})
            self.enabled = self.dc_config.get('enabled', True)
            
            # Get GitHub config
            github_config = self.dc_config.get('github', {})
            base_url = github_config.get('api_url', 'https://api.github.com')
            tokens = github_config.get('tokens', [])
            
            # Get GitLab config
            gitlab_config = self.dc_config.get('gitlab', {})
            gitlab_url = gitlab_config.get('api_url', 'https://gitlab.com')
            gitlab_token = gitlab_config.get('token')
            gitlab_enabled = gitlab_config.get('enabled', True)
            
            # Get operation config
            op_config = cfg.get('operation', {})
            max_retries = op_config.get('max_retries', max_retries)
            initial_backoff = op_config.get('initial_backoff', initial_backoff)
        else:
            # Use individual parameters
            self.cfg = {}
            self.dc_config = {}
            self.enabled = True
            base_url = 'https://api.github.com'
            tokens = [github_token] if github_token else []
            if github_tokens:
                tokens.extend(github_tokens)
            
        if not self.enabled:
            self.logger.warning("Dependency confusion check is disabled in config")
            return
            
        # Initialize GitHub clients
        if not base_url:
            base_url = 'https://api.github.com'  # Default to public GitHub API
            
        # Initialize GitHub clients with tokens
        self.github_clients = []
        self.github_client_to_token_map = {}
        self.github_rate_limits = {}
        self.github_rate_limit_resets_at = 0
        
        # Get tokens from config or parameters
        if not tokens:
            # If no tokens provided, use unauthenticated client
            self.github_client = Github(base_url=base_url)
            self.github_clients = [self.github_client]
        else:
            # Initialize clients for each token
            for token in tokens:
                try:
                    client = Github(base_url=base_url, login_or_token=token)
                    self.github_clients.append(client)
                    self.github_client_to_token_map[id(client)] = token
                    # Initialize rate limit info
                    self.github_rate_limits[token] = {
                        'remaining': 5000,  # Default GitHub rate limit
                        'reset_at': time.time() + 3600,  # Default reset time
                        'is_limited': False
                    }
                except Exception as e:
                    self.logger.error(f"Failed to initialize GitHub client with token: {e}")
            
            if self.github_clients:
                self.github_client = self.github_clients[0]
                # Create iterator for client rotation
                self._next_github_client = itertools.cycle(self.github_clients)
            else:
                self.logger.warning("No valid GitHub clients initialized. Using unauthenticated client.")
                self.github_client = Github(base_url=base_url)
                self.github_clients = [self.github_client]
        
        # Initialize GitLab client if enabled
        if gitlab_enabled:
            if not gitlab_url:
                gitlab_url = 'https://gitlab.com'  # Default to public GitLab
                
            self.gitlab_client = gitlab.Gitlab(gitlab_url, private_token=gitlab_token) if gitlab_token else gitlab.Gitlab(gitlab_url)
        else:
            self.gitlab_client = None
        
        # Initialize backoff settings
        self.max_retries = max_retries
        self.initial_backoff = initial_backoff
        self.max_backoff = 3600  # Maximum backoff in seconds (1 hour)
        self.backoff_factor = 2  # Exponential backoff factor

    @gitlab_rate_limit_aware(max_wait_time=1800)
    def _format_repo_data(self, repo_obj: Any, platform: str, org_name: str) -> Optional[Dict[str, Any]]:
        """Helper to create a consistent dictionary format, including latest commit SHA."""
        data = {
            'name': getattr(repo_obj, 'name', 'N/A'),
            'full_name': 'N/A',
            'clone_url': None,
            'html_url': None,
            'description': getattr(repo_obj, 'description', '') or '',
            'platform': platform,
            'organization': org_name,
            'archived': getattr(repo_obj, 'archived', False),
            'is_fork': False,
            'latest_commit_sha': None
        }
        repo_identifier_for_log = 'N/A'

        try:
            if platform == 'github':
                repo_identifier_for_log = getattr(repo_obj, 'full_name', 'N/A_GH')
                data['full_name'] = repo_identifier_for_log
                
                # Try the reported default branch, then fall back to main/master
                default_branch = getattr(repo_obj, 'default_branch', None)
                if not default_branch:
                    self.logger.debug(f"Skipping GitHub repo {data['full_name']} - no default branch reported.")
                    return None

                data['clone_url'] = getattr(repo_obj, 'clone_url', None)
                data['html_url'] = getattr(repo_obj, 'html_url', None)
                data['is_fork'] = getattr(repo_obj, 'fork', False)

                commit_sha = None
                try:
                    self.logger.debug(f"Attempting to fetch default branch '{default_branch}' for {data['full_name']}")
                    branch_obj = repo_obj.get_branch(default_branch)
                    commit_sha = branch_obj.commit.sha
                except GithubException as e:
                    if e.status == 404: # Not Found
                        self.logger.warning(f"Default branch '{default_branch}' not found for GitHub repo {data['full_name']}. Skipping.")
                        return None
                    elif e.status == 409 and 'Git Repository is empty' in str(e.data.get('message', '')): # Empty repository
                        self.logger.info(f"GitHub repo {data['full_name']} is empty. Skipping.")
                        return None
                    else: # Other GitHub API errors
                        self.logger.error(f"Error getting default branch '{default_branch}' for GitHub repo {data['full_name']}: {e.status} {e.data.get('message', '')}. Skipping.")
                        return None
                
                if not commit_sha:
                    self.logger.warning(f"Could not retrieve commit SHA for default branch '{default_branch}' of GitHub repo {data['full_name']}. Skipping.")
                    return None
                data['latest_commit_sha'] = commit_sha

            elif platform == 'gitlab':
                repo_identifier_for_log = getattr(repo_obj, 'path_with_namespace', 'N/A_GL')
                data['full_name'] = repo_identifier_for_log
                default_branch = getattr(repo_obj, 'default_branch', None)
                if not default_branch:
                    self.logger.debug(f"Skipping GitLab project {data['full_name']} - no default branch found.")
                    return None

                data['clone_url'] = getattr(repo_obj, 'http_url_to_repo', None)
                data['html_url'] = getattr(repo_obj, 'web_url', None)
                data['is_fork'] = bool(getattr(repo_obj, 'forked_from_project', None))
                try:
                    commits = repo_obj.commits.list(ref_name=default_branch, get_all=False, per_page=1)
                    if commits: 
                        data['latest_commit_sha'] = commits[0].id
                    else:
                        self.logger.warning(f"Could not get commit for {data['full_name']} on branch '{default_branch}'. Repo empty? Skipping.")
                        return None
                except gitlab.exceptions.GitlabGetError as e:
                    if e.response_code == 404:
                        self.logger.warning(f"Error fetching commits for GitLab {data['full_name']} (branch '{default_branch}' missing or repo empty?). Skipping.")
                        return None
                    elif e.response_code == 403:
                        self.logger.warning(f"Permission denied fetching commits for GitLab {data['full_name']}. Skipping.")
                        return None
                    else:
                        self.logger.error(f"GitLab API error getting commit for {data['full_name']}: {e}")
                        return None
                except gitlab.exceptions.GitlabConnectionError as e:
                    self.logger.error(f"GitLab connection error getting commit for {data['full_name']}: {e}")
                    return None
                except gitlab.exceptions.GitlabAuthenticationError as e:
                    self.logger.error(f"GitLab authentication error getting commit for {data['full_name']}: {e}")
                    return None

            if not data['clone_url']:
                self.logger.warning(f"Skipping repo {data['full_name']} due to missing clone URL.")
                return None
            if not data['latest_commit_sha']:
                self.logger.warning(f"Skipping repo {data['full_name']} due to missing latest commit SHA.")
                return None

        except RateLimitExceededException:
            self.logger.error(f"{platform.capitalize()} API rate limit hit for {repo_identifier_for_log}. Returning None.")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error formatting repo data for {repo_identifier_for_log} ({platform}): {e}", exc_info=True)
            return None
        return data

    def _get_next_available_github_client(self):
        """Get the next GitHub client that isn't rate limited.
        
        Returns:
            The next available GitHub client, or None if all are rate limited
        """
        if not self.github_clients:
            return None
            
        # Try each client in rotation until we find one that's not rate limited
        client_tried = set()
        
        while len(client_tried) < len(self.github_clients):
            client = next(self._next_github_client)
            client_tried.add(id(client))
            
            # Get the token associated with this client using our mapping
            token = self.github_client_to_token_map.get(id(client))
            
            # Check if we have stored rate limit info for this token
            if token and token in self.github_rate_limits:
                rate_info = self.github_rate_limits[token]
                current_time = time.time()
                
                # If we know this token is rate limited and hasn't reset yet, skip it
                if rate_info['is_limited'] and rate_info['reset_at'] > current_time:
                    self.logger.debug(f"GitHub client with token {token[:8]}... is rate limited until {datetime.fromtimestamp(rate_info['reset_at'])}. Trying next client.")
                    continue
                
                # If we have recent rate limit info and it has remaining requests, use it
                if rate_info['remaining'] > 10 and rate_info['reset_at'] > current_time:
                    self.logger.debug(f"Using GitHub client with token {token[:8]}... ({rate_info['remaining']} requests remaining)")
                    return client
            
            # If we don't have rate limit info or it's stale, check the API
            try:
                rate_limit = client.get_rate_limit()
                core_rate = rate_limit.core
                
                # Update our stored rate limit info
                if token:
                    self.github_rate_limits[token] = {
                        'remaining': core_rate.remaining,
                        'reset_at': core_rate.reset.timestamp(),
                        'is_limited': core_rate.remaining <= 10
                    }
                
                # If it has remaining requests, use it
                if core_rate.remaining > 10:  # Leave a buffer of 10 requests
                    self.logger.debug(f"Switched to GitHub client with {core_rate.remaining}/{core_rate.limit} requests remaining")
                    return client
                else:
                    # This client is rate limited, log and continue
                    reset_time = core_rate.reset.timestamp()
                    wait_time = max(0, reset_time - time.time())
                    self.logger.debug(f"GitHub client rate limited. {wait_time:.1f}s until reset. Trying next client.")
            except Exception as e:
                self.logger.warning(f"Error checking rate limit for client: {e}. Trying next client.")
        
        # If we've tried all clients and none are available, return None
        self.logger.warning("All GitHub clients are rate limited. No available clients.")
        return None

    def _handle_github_rate_limit(self, context: str = ""):
        """Handle GitHub API rate limit by checking the reset time and logging info.
        
        Args:
            context: Context string for logging where the rate limit was hit
            
        Returns:
            Reset timestamp (epoch) if rate limited, None otherwise
        """
        if not self.github_client:
            self.logger.warning("No GitHub client available to check rate limit.")
            return None
            
        try:
            # First, try to switch to another client if available
            if hasattr(self, 'github_clients') and len(self.github_clients) > 1:
                new_client = self._get_next_available_github_client()
                if new_client and new_client is not self.github_client:
                    self.logger.info(f"Switching GitHub client due to rate limit in {context}")
                    self.github_client = new_client
                    return None  # No need to wait if we switched clients
            
            # If we couldn't switch or there's only one client, check its rate limit
            rate_limit = self.github_client.get_rate_limit()
            # Core is the main REST API rate limit
            core_rate = rate_limit.core
            
            # Get the token associated with this client using our mapping
            token = self.github_client_to_token_map.get(id(self.github_client))
            
            # Update our stored rate limit info
            if token:
                self.github_rate_limits[token] = {
                    'remaining': core_rate.remaining,
                    'reset_at': core_rate.reset.timestamp(),
                    'is_limited': core_rate.remaining <= 10
                }
            
            self.logger.warning(f"GitHub rate limit status: {core_rate.remaining}/{core_rate.limit} remaining. "
                          f"Reset at {datetime.fromtimestamp(core_rate.reset.timestamp())}.")
            
            if core_rate.remaining <= 10:  # Consider rate limited if 10 or fewer requests remaining
                # We're rate limited
                reset_time = core_rate.reset.timestamp()
                wait_time = max(0, reset_time - time.time())
                self.logger.warning(f"Rate limit hit. Waiting {wait_time:.1f}s before retry.")
                # Set the reset time for the decorator to use
                self.github_rate_limit_resets_at = reset_time
                return reset_time
            
            return None  # Not rate limited
        except Exception as e:
            self.logger.error(f"Error checking GitHub rate limit: {e}")
            return None
        
    @github_rate_limit_aware(max_wait_time=3600)
    @gitlab_rate_limit_aware(max_wait_time=3600)
    def get_organization_repos(self, org_name: str, *, run_once: bool = False) -> List[Dict[str, Any]]:
        """Retrieves non-archived, non-fork repositories for an org/group, including latest commit SHA."""
        self.logger.info(f"🔎 Identifying repositories for organization/group: {Fore.MAGENTA}{org_name}{Style.RESET_ALL}")
        all_repos_data = []
        processed_full_names = set()

        # --- GitHub ---
        if self.github_client:
            try:
                # First check if we can access the organization
                try:
                    self.logger.debug(f"Checking GitHub API access for organization: {org_name}")
                    gh_org = self.github_client.get_organization(org_name)
                except GithubException as e:
                    if e.status == 403 and 'rate limit exceeded' in str(e).lower():
                        # Handle rate limit at organization level
                        reset_time = self._handle_github_rate_limit(f"org {org_name}")
                        if reset_time:
                            wait_time = max(0, reset_time - time.time())
                            if not run_once and wait_time < 3600:  # Max 1 hour wait
                                self.logger.warning(f"Rate limit hit while checking org {org_name}. Waiting {wait_time:.1f}s.")
                                time.sleep(wait_time + 1)  # Add 1 second buffer
                                # Try to switch to another client before retrying
                                if hasattr(self, 'github_clients') and len(self.github_clients) > 1:
                                    new_client = self._get_next_available_github_client()
                                    if new_client and new_client is not self.github_client:
                                        self.logger.info(f"Switching GitHub client before retrying org {org_name}")
                                        self.github_client = new_client
                                return self.get_organization_repos(org_name, run_once=run_once)  # Retry
                            else:
                                self.logger.error(f"Rate limit exceeded for org {org_name} and wait time too long ({wait_time:.1f}s). Skipping.")
                                return []
                    elif e.status == 404:
                        self.logger.warning(f"🐙 GitHub organization '{org_name}' not found (404).")
                        return []
                    else:
                        self.logger.error(f"❌ GitHub API error for org '{org_name}': {e.status} {e.data.get('message', 'N/A')}")
                        return []
                
                # If we get here, we have access to the organization
                self.logger.debug(f"Fetching repositories for '{org_name}' with automatic pagination")
                count = 0
                try:
                    for repo in gh_org.get_repos(type='sources'):
                        try:
                            if repo.archived:
                                self.logger.debug(f"Skipping archived GitHub repo: {repo.full_name}")
                                continue

                            repo_data = self._format_repo_data(repo, 'github', org_name)
                            if repo_data and repo_data['full_name'] not in processed_full_names:
                                all_repos_data.append(repo_data)
                                processed_full_names.add(repo_data['full_name'])
                                count += 1
                                if count % 50 == 0: 
                                    self.logger.debug(f"  ... identified {count} GitHub repos for '{org_name}'")
                            elif repo_data: 
                                self.logger.warning(f"Duplicate repo full_name (GitHub): {repo_data['full_name']}. Skipping.")
                        except UnknownObjectException:
                            self.logger.error(f"Encountered 'Not Found' for a repo under '{org_name}'. Skipping repo.")
                            continue
                        except GithubException as e:
                            if e.status == 403 and 'rate limit exceeded' in str(e).lower():
                                # Handle rate limit at repo level
                                reset_time = self._handle_github_rate_limit(f"repo {repo.full_name}")
                                if reset_time:
                                    wait_time = max(0, reset_time - time.time())
                                    if not run_once and wait_time < 3600:  # Max 1 hour wait
                                        self.logger.warning(f"Rate limit hit while processing {repo.full_name}. Waiting {wait_time:.1f}s.")
                                        time.sleep(wait_time + 1)  # Add 1 second buffer
                                        # Try to switch to another client before retrying
                                        if hasattr(self, 'github_clients') and len(self.github_clients) > 1:
                                            new_client = self._get_next_available_github_client()
                                            if new_client and new_client is not self.github_client:
                                                self.logger.info(f"Switching GitHub client before retrying repo {repo.full_name}")
                                                self.github_client = new_client
                                        continue  # Retry this repo
                                    else:
                                        self.logger.error(f"Rate limit exceeded for repo {repo.full_name} and wait time too long ({wait_time:.1f}s). Skipping.")
                                        continue
                            self.logger.error(f"❌ GitHub API error fetching repo details for '{org_name}': Status {e.status} - {e.data.get('message', 'N/A')}. Skipping repo.")
                            continue
                        except Exception as e:
                            self.logger.error(f"💥 Unexpected error processing GitHub repo for '{org_name}': {e}", exc_info=True)
                            continue
                    
                    self.logger.info(f"🐙 Found {count} valid source repositories on GitHub for '{org_name}'.")
                        
                except Exception as e:
                    self.logger.error(f"💥 Unexpected error fetching GitHub repositories for '{org_name}': {e}", exc_info=True)
            except Exception as e: 
                self.logger.error(f"💥 Unexpected error (GitHub) for '{org_name}': {e}", exc_info=True)

        # --- GitLab ---
        if self.gitlab_client:
            try:
                self.logger.debug(f"Querying GitLab API for group: {org_name}")
                target_group = self._get_gitlab_group(org_name)
                
                if target_group:
                    self.logger.debug(f"Found GitLab group '{target_group.full_path}'. Fetching projects...")
                    count = 0
                    try:
                        projects = self._get_gitlab_projects(target_group)
                        for project in projects:
                            if bool(getattr(project, 'forked_from_project', None)):
                                self.logger.debug(f"Skipping GitLab fork: {project.path_with_namespace}")
                                continue

                            repo_data = self._format_repo_data(project, 'gitlab', org_name)
                            if repo_data and repo_data['full_name'] not in processed_full_names:
                                all_repos_data.append(repo_data)
                                processed_full_names.add(repo_data['full_name'])
                                count += 1
                                if count % 50 == 0: 
                                    self.logger.debug(f"  ... identified {count} GitLab projects for '{org_name}'")
                            elif repo_data: 
                                self.logger.warning(f"Duplicate repo full_name (GitLab): {repo_data['full_name']}. Skipping.")

                        self.logger.info(f"🦊 Found {count} valid, non-fork projects on GitLab for group '{org_name}'.")
                    except Exception as e:
                        self.logger.error(f"💥 Unexpected error listing GitLab projects for '{org_name}': {e}", exc_info=True)
            except gitlab.exceptions.GitlabAuthenticationError as e:
                self.logger.error(f"❌ GitLab authentication error for '{org_name}': {e}")
            except gitlab.exceptions.GitlabConnectionError as e:
                self.logger.error(f"❌ GitLab connection error for '{org_name}': {e}")
            except Exception as e: 
                self.logger.error(f"💥 Unexpected error (GitLab) for '{org_name}': {e}", exc_info=True)

        # --- Summary ---
        total_found = len(all_repos_data)
        if total_found == 0: 
            self.logger.warning(f"🤷 No valid repositories identified for '{org_name}' on configured platforms.")
        else: 
            self.logger.info(f"Identified {total_found} unique, valid repositories for '{org_name}' across platforms.")
        return all_repos_data
        
    @github_rate_limit_aware(max_wait_time=1800)
    @gitlab_rate_limit_aware(max_wait_time=1800)
    def get_changed_package_files(self, repo_full_name: str, platform: str, package_file_patterns: List[str], base_sha: Optional[str] = None, head_sha: Optional[str] = None) -> List[str]:
        """
        Get package files. If base_sha is None, lists all matching files at head_sha (or default branch).
        If base_sha is provided, lists files changed between base_sha and head_sha (or default branch).

        Args:
            repo_full_name: Full repository name (org/repo).
            platform: The platform ('github' or 'gitlab').
            package_file_patterns: List of package file patterns to match.
            base_sha: Optional base commit SHA for differential scan.
            head_sha: Optional head commit SHA. Defaults to the repo's default branch if None.

        Returns:
            List of package file paths.
        """
        changed_files: List[str] = []
        self.logger.info(f"Getting package files for {repo_full_name} on {platform}. Base: {base_sha or 'None'}, Head: {head_sha or 'Default'}.")

        try:
            if platform == 'github':
                if not self.github_client:
                    self.logger.error("GitHub client not available.")
                    return []
                repo = self.github_client.get_repo(repo_full_name)
                effective_head_sha = head_sha or repo.default_branch

                if base_sha is None: # Initial scan or force scan all
                    self.logger.debug(f"Performing full file list for {repo_full_name} at {effective_head_sha}")
                    tree = repo.get_git_tree(sha=effective_head_sha, recursive=True).tree
                    for element in tree:
                        if element.type == 'blob' and any(fnmatch.fnmatch(element.path, pattern) for pattern in package_file_patterns):
                            changed_files.append(element.path)
                else: # Differential scan
                    self.logger.debug(f"Performing diff scan for {repo_full_name} from {base_sha} to {effective_head_sha}")
                    comparison = repo.compare(base_sha, effective_head_sha)
                    for file in comparison.files:
                        if any(fnmatch.fnmatch(file.filename, pattern) for pattern in package_file_patterns):
                            changed_files.append(file.filename)

            elif platform == 'gitlab':
                if not self.gitlab_client:
                    self.logger.error("GitLab client not available.")
                    return []
                project = self.gitlab_client.projects.get(repo_full_name)
                effective_head_sha = head_sha or project.default_branch

                if base_sha is None: # Initial scan or force scan all
                    self.logger.debug(f"Performing full file list for {repo_full_name} at {effective_head_sha}")
                    items = project.repository_tree(ref=effective_head_sha, recursive=True, get_all=True)
                    for item in items:
                        if item['type'] == 'blob' and any(fnmatch.fnmatch(item['path'], pattern) for pattern in package_file_patterns):
                            changed_files.append(item['path'])
                else: # Differential scan
                    self.logger.debug(f"Performing diff scan for {repo_full_name} from {base_sha} to {effective_head_sha}")
                    diff_data = project.repository_compare(base_sha, effective_head_sha)
                    for diff_item in diff_data.get('diffs', []): # .get('diffs', []) for safety
                        # Consider new_path; old_path for renames might also be relevant if files move
                        # but for dependency files, new_path is usually what matters.
                        if any(fnmatch.fnmatch(diff_item['new_path'], pattern) for pattern in package_file_patterns):
                            changed_files.append(diff_item['new_path'])
            else:
                self.logger.error(f"Unsupported platform: {platform}")
                return []

        except UnknownObjectException as e: # Specific to GitHub
            self.logger.error(f"GitHub error (UnknownObjectException) for {repo_full_name} (base: {base_sha}, head: {head_sha}): {e.status} {e.data.get('message','')}")
            raise ScanUncertainException(f"Failed to get changes for {repo_full_name} due to missing SHA or ref.") from e
        except GithubException as e:
            self.logger.error(f"GitHub API error for {repo_full_name}: {e.status} {e.data.get('message','')}")
            raise ScanUncertainException(f"GitHub API error prevented getting changes for {repo_full_name}.") from e
        except gitlab.exceptions.GitlabGetError as e: # Specific to GitLab for 404 etc.
            self.logger.error(f"GitLab API error (GitlabGetError) for {repo_full_name} (base: {base_sha}, head: {head_sha}): {e.response_code} {e.error_message}")
            raise ScanUncertainException(f"Failed to get changes for {repo_full_name} due to missing SHA or ref (GitLab).") from e
        except gitlab.exceptions.GitlabHttpError as e: # Broader GitLab HTTP errors
            self.logger.error(f"GitLab HTTP error for {repo_full_name}: {e.response_code} {e.error_message}")
            raise ScanUncertainException(f"GitLab HTTP error prevented getting changes for {repo_full_name}.") from e
        except Exception as e:
            self.logger.error(f"Unexpected error getting changed files for {repo_full_name} on {platform}: {e}", exc_info=True)
            raise ScanUncertainException(f"Unexpected error prevented getting changes for {repo_full_name}.") from e
                
        self.logger.info(f"Found {len(changed_files)} package files for {repo_full_name} on {platform} (Base: {base_sha or 'None'}, Head: {head_sha or 'Default'}).")
        return changed_files
        
    @gitlab_rate_limit_aware(max_wait_time=1800)
    def _get_gitlab_group(self, org_name: str) -> Optional[Any]:
        """Get a GitLab group by name, with rate limit handling."""
        try:
            return self.gitlab_client.groups.get(org_name)
        except gitlab.exceptions.GitlabGetError as get_err:
            if get_err.response_code == 404:
                self.logger.debug(f"GitLab group '{org_name}' not found by path/ID. Searching...")
                # Use get_all=True to retrieve all matching groups
                groups = self.gitlab_client.groups.list(search=org_name, get_all=True)
                matched = [g for g in groups if g.full_path.lower() == org_name.lower()]
                if len(matched) == 1:
                    return matched[0]
                elif len(matched) > 1:
                    self.logger.warning(f"Multiple GitLab groups match '{org_name}'. Using first: {matched[0].full_path}")
                    return matched[0]
                else:
                    self.logger.warning(f"🦊 GitLab group '{org_name}' not found via search (404).")
            else:
                self.logger.error(f"❌ GitLab API error getting group '{org_name}': {get_err}")
            return None

    @gitlab_rate_limit_aware(max_wait_time=1800)
    def _get_gitlab_projects(self, group: Any) -> List[Any]:
        """Get projects from a GitLab group, with rate limit handling."""
        try:
            return list(group.projects.list(archived=False, include_subgroups=True, all=True))
        except gitlab.exceptions.GitlabListError as e:
            self.logger.error(f"❌ GitLab API error listing projects: {e}")
            return []
        except gitlab.exceptions.GitlabConnectionError as e:
            self.logger.error(f"❌ GitLab connection error listing projects: {e}")
            return []
        except Exception as e:
            self.logger.error(f"💥 Unexpected error listing GitLab projects: {e}", exc_info=True)
            return []

    @github_rate_limit_aware(max_wait_time=1800)
    @gitlab_rate_limit_aware(max_wait_time=1800)
    def get_file_content(self, repo_full_name: str, file_path: str, platform: str, ref: Optional[str] = None) -> Optional[str]:
        """
        Retrieves the content of a file from a repository.

        Args:
            repo_full_name: The full name of the repository (e.g., 'org/repo').
            file_path: The path to the file within the repository.
            platform: A string indicating the platform ('github' or 'gitlab').
            ref: Optional commit SHA, branch name, or tag. Uses default branch if None.

        Returns:
            The decoded file content as a string, or None if an error occurs.
        """
        self.logger.debug(f"Attempting to fetch content for '{file_path}' in '{repo_full_name}' on {platform} (ref: {ref or 'default branch'})")

        if platform == 'github':
            if not self.github_client:
                self.logger.error("GitHub client not initialized. Cannot fetch file content.")
                return None
            try:
                repo = self.github_client.get_repo(repo_full_name)
                # If ref is None, PyGithub uses the default branch automatically.
                file_contents_obj = repo.get_contents(file_path, ref=ref)
                if file_contents_obj.type == "dir":
                    self.logger.warning(f"Path '{file_path}' in '{repo_full_name}' is a directory, not a file. Cannot get content.")
                    return None
                if file_contents_obj.encoding is None and file_contents_obj.content is None: # check for submodule
                    self.logger.warning(f"Path '{file_path}' in '{repo_full_name}' appears to be a submodule or empty. Cannot get content.")
                    return None

                decoded_content = file_contents_obj.decoded_content
                return decoded_content.decode('utf-8')
            except UnknownObjectException:
                self.logger.warning(f"File '{file_path}' not found in GitHub repo '{repo_full_name}' (ref: {ref or 'default branch'}).")
                return None
            except RateLimitExceededException as rle:
                self.logger.error(f"Rate limit exceeded while fetching file from GitHub: {rle}")
                self._handle_github_rate_limit(f"get_file_content for {repo_full_name}/{file_path}")
                return None # Or could retry after waiting if decorator doesn't handle it fully for this specific call pattern
            except GithubException as ge:
                self.logger.error(f"GitHub API error fetching file '{file_path}' from '{repo_full_name}': {ge.status} {ge.data.get('message', '')}")
                return None
            except Exception as e:
                self.logger.error(f"Unexpected error fetching file from GitHub '{repo_full_name}/{file_path}': {e}", exc_info=True)
                return None

        elif platform == 'gitlab':
            if not self.gitlab_client:
                self.logger.error("GitLab client not initialized. Cannot fetch file content.")
                return None
            try:
                project = self.gitlab_client.projects.get(repo_full_name)
                effective_ref = ref or project.default_branch
                file_obj = project.files.get(file_path=file_path, ref=effective_ref)
                # The content is base64 encoded by default
                return file_obj.decode().decode('utf-8')
            except gitlab.exceptions.GitlabGetError as gge:
                if gge.response_code == 404:
                    self.logger.warning(f"File '{file_path}' not found in GitLab repo '{repo_full_name}' (ref: {effective_ref}).")
                else:
                    self.logger.error(f"GitLab API error (GetError) fetching file '{file_path}' from '{repo_full_name}': {gge.response_code} - {gge.error_message}")
                return None
            except gitlab.exceptions.GitlabHttpError as ghe: # Catch other HTTP errors
                self.logger.error(f"GitLab HTTP error fetching file '{file_path}' from '{repo_full_name}': {ghe.response_code} - {ghe.error_message}")
                return None
            except Exception as e:
                self.logger.error(f"Unexpected error fetching file from GitLab '{repo_full_name}/{file_path}': {e}", exc_info=True)
                return None
        else:
            self.logger.error(f"Unsupported platform '{platform}' for get_file_content.")
            return None
        