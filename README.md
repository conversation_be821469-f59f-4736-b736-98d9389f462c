# 🛡️ DepConf Checker

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Tests](https://img.shields.io/badge/tests-passing-brightgreen.svg)](tests/)

> A powerful command-line tool for detecting dependency confusion vulnerabilities across multiple package ecosystems. 🚀

## ✨ Features

### 🔍 Multi-Ecosystem Support
- **Python/pip**: `requirements.txt`, `requirements.in`, `pyproject.toml`
- **JavaScript/npm**: `package.json`
- **Ruby/RubyGems**: `Gemfile`
- **Java/Maven**: `pom.xml`
- **PHP/Composer**: `composer.json`
- **Go**: `go.mod`
- **.NET/NuGet**: `packages.config`, `*.csproj`

### 🚀 Core Capabilities
- **Smart Scanning**: Only processes repositories when package files change
- **Vulnerability Detection**: Identifies dependency confusion, missing public packages, and potential typosquatting issues
- **Flexible Deployment**: Works with remote repositories or local paths
- **Continuous Monitoring**: Periodic organization-wide vulnerability checks
- **Rich Notifications**: Multi-channel alerts via:
  - 📱 Telegram
  - 💬 Discord
  - 💼 Slack
  - 📧 Microsoft Teams
  - 📨 Email (SMTP)
- **Comprehensive Reports**: JSON and Markdown summaries
- **Rate Limit Handling**: Automatic retry with exponential backoff
- **Proxy Support**: Optional SOCKS proxy configuration

## 🛠️ Prerequisites

- **Python 3.8+**
- **Git** (in system PATH)
- **GitHub/GitLab Access Token** (recommended for rate limit handling)

## 📦 Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/sl4x0/depconf.git
   cd depconf
   ```

2. **Set up Python environment:**
   ```bash
   # Create and activate virtual environment
   python -m venv venv
   
   # Linux/Mac
   source venv/bin/activate
   
   # Windows
   .\venv\Scripts\activate
   
   # Install base package
   pip install -e .
   ```

3. **Optional Features:**
   ```bash
   # GitLab support
   pip install -e .[gitlab]
   
   # Async HTTP checks
   pip install -e .[async]
   
   # Advanced typosquatting detection
   pip install -e .[levenshtein,jellyfish]
   
   # SOCKS proxy support
   pip install -e .[socks]
   
   # All features
   pip install -e .[all]
   ```

## ⚙️ Configuration

The tool is configured via a YAML file. For security and maintainability:

1. Copy the example configuration:
   ```bash
   cp depconf_config.yaml.example depconf_config.yaml
   ```

2. Configure your settings:
   - Use environment variables for sensitive data (tokens, credentials)
   - Adjust scanning parameters based on your needs
   - Configure notification channels as needed

### 🔐 Security Best Practices

- Store tokens in environment variables:
  ```bash
  export GITHUB_TOKEN="your-token"
  export GITHUB_TOKENS="token1,token2"  # For multiple tokens
  export GITLAB_TOKEN="your-token"
  ```

- Use secure notification channels:
  - Enable 2FA for notification services
  - Use webhook secrets where available
  - Configure SMTP with TLS

### 📋 Configuration Reference

<details>
<summary>Click to expand configuration reference</summary>

```yaml
# --- Scanner Operation ---
operation:
  concurrency: 3
  shallow_clone_timeout: 300
  scan_interval: 86400
  package_file_patterns:
    - 'package.json'
    - 'requirements*.txt'
    - 'pyproject.toml'
    - 'Pipfile'
    - 'Pipfile.lock'
    - 'yarn.lock'
    - 'package-lock.json'
    - 'poetry.lock'
    - 'Gemfile'
    - 'pom.xml'
    - 'composer.json'
    - 'go.mod'
    - 'packages.config'
    - '*.csproj'

# --- Notification Settings ---
notifications:
  telegram:
    enabled: false
    bot_token: ""
    chat_id: ""
  discord:
    enabled: false
    webhook_url: ""
  slack:
    enabled: false
    webhook_url: ""
  teams:
    enabled: false
    webhook_url: ""
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    smtp_username: ""
    smtp_password: ""
    from_address: ""
    to_addresses: []

# --- Dependency Confusion Settings ---
dependency_confusion:
  enabled: true
  http_timeout: 20
  http_retries: 2
  enable_async: true
  async_ssl_verify: true
  max_concurrent_requests: 10
  connection_limit: 100
  
  exclude_packages: []
  exclude_packages_pip: []
  exclude_packages_npm: []
  exclude_packages_rubygems: []
  exclude_packages_maven: []
  exclude_packages_composer: []
  exclude_packages_gomodules: []
  exclude_packages_nuget: []
  
  internal_package_pattern: ""
  secure_namespaces: []
  
  enable_typosquatting_detection: true
  typosquatting_threshold: 2
  typosquatting_length_divisor: 4
  
  proxies: []
  proxy_rotation: false
  proxy_rotation_factor: 5

# --- Organizations to Scan ---
organizations:
  - your-github-org
```
</details>

## 🚀 Usage

### One-Time Scan
```bash
depconf check --config depconf_config.yaml
```

### Continuous Monitoring
```bash
depconf monitor --config depconf_config.yaml
```

### Local Repository Scan
```bash
depconf local /path/to/repo --config depconf_config.yaml
```

## 📊 Output

The tool generates:
- `findings.json`: Detailed JSON report
- `findings.md`: Human-readable Markdown report
- `repo_state.json`: Scan state tracking

## 🔄 How It Works

1. **Repository Discovery** 🔍
   - Identifies repositories in specified organizations
   - Supports both GitHub and GitLab
   - Handles rate limits with exponential backoff

2. **Smart Scanning** 🧠
   - Only processes changed repositories
   - Tracks state between runs
   - Optimizes performance with concurrent scanning

3. **Dependency Analysis** 📦
   - Analyzes package manifests
   - Detects vulnerabilities
   - Supports custom exclusions
   - Implements typosquatting detection

4. **Notification System** 📢
   - Multi-channel alerts
   - Rich formatting
   - Configurable delivery
   - Rate-limited sending

5. **State Management** 📝
   - Tracks scan history
   - Optimizes performance
   - Prevents redundant scans
   - Maintains audit trail

## 🎯 Command-Line Options

### Global Options
- `--config, -c`: Configuration file path
- `--output`: Override output directory
- `--log-level`: Set logging level

### Commands
- `check`: One-time organization scan
- `monitor`: Continuous monitoring
- `local`: Local repository scan

## 🧪 Testing

Run the test suite:
```bash
# Install test dependencies
pip install -e .[test]

# Run tests
pytest

# Run with coverage
pytest --cov=depconf
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and ensure they pass
5. Submit a pull request

### Development Setup
```bash
# Install development dependencies
pip install -e .[dev]

# Run linting
black .
flake8

# Run type checking
mypy depconf
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
