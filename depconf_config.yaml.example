general:
  log_level: INFO # debug, info, warning, error, critical
  output_dir: ./depconf_results

# --- Platform Credentials & Settings ---
github:
  # Required for listing org repos (especially private) and avoiding rate limits
  # SECURITY NOTE: Use environment variables instead of hardcoding tokens
  # Example: export GITHUB_TOKEN="your-token" and export GITHUB_TOKENS="token1,token2"
  tokens: []  # Set via GITHUB_TOKEN or GITHUB_TOKENS environment variables
  # Rate limit handling settings
  rate_limit_retry: true
  rate_limit_retry_count: 3
  rate_limit_retry_delay: 60  # seconds
  rate_limit_retry_backoff: 2  # exponential backoff multiplier

gitlab:
  # Enable if scanning GitLab groups listed in 'organizations'
  enabled: false
  # Optional: Required for private GitLab groups/repos
  # SECURITY NOTE: Use environment variables instead of hardcoding tokens
  # Example: export GITLAB_TOKEN="your-token"
  token: ""  # Set via GITLAB_TOKEN environment variable
  # Optional: Override for self-hosted GitLab instances
  api_url: "https://gitlab.com/api/v4"

# --- Notification Settings ---
notifications:
  telegram:
    enabled: false
    bot_token: ""  # Set via TELEGRAM_BOT_TOKEN environment variable
    chat_id: ""    # Set via TELEGRAM_CHAT_ID environment variable
  discord:
    enabled: false
    webhook_url: ""  # Set via DISCORD_WEBHOOK_URL environment variable
  slack:
    enabled: false
    webhook_url: ""  # Set via SLACK_WEBHOOK_URL environment variable
  teams:
    enabled: false
    webhook_url: ""  # Set via TEAMS_WEBHOOK_URL environment variable
  email:
    enabled: false
    smtp_server: ""     # Set via SMTP_SERVER environment variable
    smtp_port: 587
    smtp_username: ""   # Set via SMTP_USERNAME environment variable
    smtp_password: ""   # Set via SMTP_PASSWORD environment variable
    from_address: ""    # Set via SMTP_FROM_ADDRESS environment variable
    to_addresses: []    # Set via SMTP_TO_ADDRESSES environment variable (comma-separated)

# --- Dependency Confusion Check Settings ---
dependency_confusion:
  # Core settings
  enabled: true
  http_retries: 5
  http_timeout: 30
  enable_async: true
  async_ssl_verify: true
  max_concurrent_requests: 5
  connection_limit: 50
  request_delay: 1  # Added delay between requests in seconds

  # Rate limit handling
  rate_limit_handling:
    enabled: true
    retry_count: 3
    retry_delay: 60
    backoff_factor: 2
    max_retry_delay: 3600  # 1 hour max delay

  # Package exclusion lists
  exclude_packages: []  # General exclusion list
  exclude_packages_pip: []  # Python/pip package exclusions
  exclude_packages_npm: []  # NPM package exclusions
  exclude_packages_rubygems: []  # Ruby gem exclusions
  exclude_packages_maven: []  # Maven artifact exclusions
  exclude_packages_composer: []  # PHP/Composer package exclusions
  exclude_packages_gomodules: []  # Go module exclusions
  exclude_packages_nuget: []  # NuGet package exclusions

  # Internal package settings
  internal_package_pattern: ""  # Regex pattern for internal packages
  secure_namespaces: []  # List of trusted namespaces/scopes

  # Typosquatting detection
  enable_typosquatting_detection: true
  typosquatting_threshold: 2
  typosquatting_length_divisor: 4

  # Proxy settings
  proxies: []  # List of proxy URLs
  proxy_rotation: false
  proxy_rotation_factor: 5

# --- Operation Settings ---
operation:
  # List of file patterns to consider as package files (glob format)
  package_file_patterns:
    - "package.json"
    - "requirements*.txt"
    - "pyproject.toml"
    - "Pipfile"
    - "Pipfile.lock"
    - "yarn.lock"
    - "package-lock.json"
    - "poetry.lock"
    - "Gemfile"
    - "pom.xml"
    - "composer.json"
    - "go.mod"
    - "*.csproj"
    - "packages.config"
  concurrency: 2
  shallow_clone_timeout: 300  # 5 minutes
  scan_interval: 43200  # 12 hours (in seconds)
  batch_size: 10  # Process repositories in smaller batches
  batch_delay: 60  # Delay between batches in seconds

# --- Target Organizations ---
# Add your organizations here
organizations:
  # Example organizations (replace with your own)
  # - your-github-org
  # - your-gitlab-group
