# depconf/checker.py

import os
import logging
import re
import json # For parsing package.json
import urllib.parse # For URL encoding npm package names
import xml.etree.ElementTree as ET  # For parsing Maven POM files
import fnmatch # For filename pattern matching in _get_package_data
import asyncio  # For async/parallel execution
from typing import List, Dict, Any, Optional

# Default configuration values
DEFAULT_CLONE_TIMEOUT = 300  # 5 minutes
DEFAULT_MAX_RETRIES = 3
DEFAULT_RETRY_DELAY = 5  # seconds

# For typosquatting detection
try:
    import Levenshtein
    HAVE_LEVENSHTEIN = True
except ImportError:
    HAVE_LEVENSHTEIN = False

# For advanced string similarity
try:
    import jellyfish
    HAVE_JELLYFISH = True
except ImportError:
    HAVE_JELLYFISH = False

# HTTP libraries for registry checks
try:
    import requests
    from requests.exceptions import Timeout, RequestException, ProxyError
    from requests.adapters import HTTPAdapter
    from urllib3.util.retry import Retry
    HAVE_REQUESTS = True
except ImportError:
    HAVE_REQUESTS = False

# For async HTTP requests
try:
    import aiohttp
    from aiohttp import ClientTimeout, ClientError, ClientResponseError, ClientConnectorError
    HAVE_AIOHTTP = True
except ImportError:
    HAVE_AIOHTTP = False

# For SOCKS proxy support
try:
    import socks
    HAVE_SOCKS = True
except ImportError:
    HAVE_SOCKS = False

# Import version for User-Agent
from . import __version__
from .repo_identifier import RepositoryIdentifier # Added for type hinting

# Try to import a TOML parser - prefer stdlib tomllib (Python 3.11+) or fallback to tomli
try:
    try:
        import tomllib  # Python 3.11+
    except ImportError:
        import tomli as tomllib  # Python <3.11, requires installation
    HAVE_TOML_PARSER = True
except ImportError:
    HAVE_TOML_PARSER = False
    # Warning moved to DependencyChecker.__init__ to avoid noise on module import

# Common User-Agent string for all HTTP requests
USER_AGENT = f'depconf-checker/{__version__} (https://github.com/sl4x0/depconf)'

logger = logging.getLogger('depconf.checker')

# HTTP session will be created in the DependencyChecker class
# This allows us to configure proxies based on the configuration

# Normalize package name according to PyPI standards (PEP 503)
def normalize_pypi_package_name(name: str) -> str: # Renamed for clarity
    """Normalize package name according to PEP 503 for PyPI."""
    return re.sub(r"[-_.]+", "-", name).lower()

# For npm, names are case-sensitive but often normalized to lowercase for lookup consistency
# Scoped packages like @scope/name are common.
# We won't aggressively normalize npm names here but ensure they are URL-safe for API calls.
# The actual name from package.json is what we should generally use.

# Normalization functions for new ecosystems
def normalize_rubygems_package_name(name: str) -> str:
    """Normalize Ruby gem name for comparison and caching."""
    return name.lower()

def normalize_maven_artifact_id(name: str) -> str:
    """Normalize Maven artifact ID for internal pattern matching."""
    return name.lower()

def normalize_maven_coordinates_for_cache(group_id: str, artifact_id: str) -> str:
    """Create a normalized string representation of Maven coordinates for caching."""
    return f"{group_id.lower()}:{artifact_id.lower()}"

def normalize_composer_package_name(name: str) -> str:
    """Normalize Composer/Packagist package name (vendor/package)."""
    return name.lower()

def normalize_gomodule_path(name: str) -> str:
    """Normalize Go module path for caching and proxy access.
    
    Go's module proxy uses a special encoding for uppercase letters:
    uppercase letters are escaped using a '!' followed by the lowercase letter.
    For example, 'A' becomes '!a'.
    """
    # For caching purposes, we'll just use lowercase
    return name.lower()

def normalize_nuget_package_id(name: str) -> str:
    """Normalize NuGet package ID."""
    return name.lower()


def calculate_levenshtein_distance(str1: str, str2: str) -> int:
    """Calculate Levenshtein distance between two strings.
    
    If python-Levenshtein is available, use it for better performance.
    Otherwise, fall back to a simple implementation.
    
    Args:
        str1: First string to compare
        str2: Second string to compare
        
    Returns:
        Integer representing the edit distance between the strings
    """
    if HAVE_LEVENSHTEIN:
        return Levenshtein.distance(str1, str2)
    else:
        # Fallback implementation if Levenshtein library is not available
        # Dynamic programming approach to calculate edit distance
        len_str1 = len(str1) + 1
        len_str2 = len(str2) + 1
        
        # Initialize matrix of zeros
        matrix = []
        for i in range(len_str1):
            matrix.append([0] * len_str2)
        
        # Fill in the first row and column
        for i in range(len_str1):
            matrix[i][0] = i
        for j in range(len_str2):
            matrix[0][j] = j
        
        # Fill the matrix
        for i in range(1, len_str1):
            for j in range(1, len_str2):
                if str1[i-1] == str2[j-1]:
                    cost = 0
                else:
                    cost = 1
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      # deletion
                    matrix[i][j-1] + 1,      # insertion
                    matrix[i-1][j-1] + cost  # substitution
                )
                
        return matrix[len_str1-1][len_str2-1]

class DependencyChecker:
    """Handles dependency confusion checks for repositories."""
    
    def __init__(self, dep_conf_settings: Dict[str, Any], repo_identifier: RepositoryIdentifier):
        """Initialize the dependency checker with configuration."""
        self.repo_identifier = repo_identifier # Store RepositoryIdentifier instance
        self.logger = logging.getLogger('depconf.checker')
        self.session = None  # Will be initialized by _init_http_session

        # Check for missing dependencies and warn user
        if not HAVE_REQUESTS:
            self.logger.warning("requests library not available. HTTP checks will be disabled.")
        if not HAVE_AIOHTTP:
            self.logger.warning("aiohttp library not available. Async HTTP checks will be disabled.")
        if not HAVE_TOML_PARSER:
            self.logger.warning("TOML parser not available. pyproject.toml files will be skipped.")
        if not HAVE_LEVENSHTEIN:
            self.logger.info("python-Levenshtein not available. Using slower string similarity.")
        if not HAVE_JELLYFISH:
            self.logger.info("jellyfish not available. Advanced typosquatting detection disabled.")
        
        # Load configuration from the 'dependency_confusion' section of cfg
        # It's assumed cfg here is the fully resolved config from ConfigManager
        # dep_conf_settings = cfg.get('dependency_confusion', {})

        self.enabled = dep_conf_settings.get('enabled', True) # Default to True if not specified

        # Rely on ConfigManager to provide defaults if not in cfg
        self.supported_files = dep_conf_settings.get('supported_files')
        self.excluded_dirs = set(dep_conf_settings.get('excluded_dirs'))
        
        # Typosquatting settings
        self.typosquatting_threshold = dep_conf_settings.get('typosquatting_threshold')
        self.typosquatting_length_divisor = dep_conf_settings.get('typosquatting_length_divisor')
        self.top_packages = dep_conf_settings.get('top_packages')
        self.enable_typosquatting_detection = dep_conf_settings.get('enable_typosquatting_detection', True)

        # Initialize new internal attributes for proxy, timeout, and SSL verify settings
        self.proxy_config_val = dep_conf_settings.get('proxy', {})
        self.http_timeout_val = dep_conf_settings.get('timeout', 30)
        self.ssl_verify_val = dep_conf_settings.get('ssl_verify', True)

        # Internal package identification settings
        self.secure_namespaces = set(dep_conf_settings.get('secure_namespaces', []))
        self.internal_package_regex = None
        internal_pattern_str = dep_conf_settings.get('internal_package_pattern')
        if internal_pattern_str:
            try:
                self.internal_package_regex = re.compile(internal_pattern_str)
                self.logger.info(f"Using internal package regex: {internal_pattern_str}")
            except re.error as e:
                self.logger.error(f"Invalid regex for 'internal_package_pattern': {e}. Pattern will be ignored.")
        
        # Load registry URLs
        self.registry_urls = dep_conf_settings.get('registry_urls')
        self.PYPI_REGISTRY_URL = self.registry_urls['pip']
        self.NPM_REGISTRY_URL = self.registry_urls['npm']
        self.RUBYGEMS_REGISTRY_URL = self.registry_urls['rubygems']
        self.MAVEN_REGISTRY_URL = self.registry_urls['maven']
        self.COMPOSER_REGISTRY_URL = self.registry_urls['composer']
        self.GOMODULES_REGISTRY_URL = self.registry_urls['gomodules']
        self.NUGET_REGISTRY_URL = self.registry_urls['nuget']

        # Warn if TOML parser is not available
        if not HAVE_TOML_PARSER:
            self.logger.warning("TOML parser not available. PyProject.toml files will not be processed.")
            
        # Warn if string similarity libraries are not available
        if not HAVE_LEVENSHTEIN:
            self.logger.warning("python-Levenshtein not available. Using slower pure Python implementation.")
        if not HAVE_JELLYFISH:
            self.logger.warning("jellyfish not available. Some string similarity features may be limited.")
            
        # Warn if aiohttp is not available
        if not HAVE_AIOHTTP:
            self.logger.warning("aiohttp not available. Async features will be disabled.")
            
        # Warn if SOCKS proxy support is not available
        if not HAVE_SOCKS:
            self.logger.warning("SOCKS proxy support not available. Only HTTP/HTTPS proxies will work.")
        
    async def _init_http_session(self) -> None:
        """Initialize HTTP session with proper configuration.
        
        Args:
            cfg: Full configuration dictionary
        """
        # Get proxy settings from dependency_confusion section
        # self.dc_config already refers to cfg.get('dependency_confusion', {})
        proxy_config = self.proxy_config_val
        use_proxy = proxy_config.get('enabled', False)
        proxy_url = proxy_config.get('url')
        proxy_type = proxy_config.get('type', 'http')
        
        # Configure timeout
        timeout_seconds = self.http_timeout_val # Default to 30s
        timeout = ClientTimeout(total=timeout_seconds)
        
        # Configure SSL verification
        ssl_verify = self.ssl_verify_val # Used by proxy connector if created
        
        # Create session with proper configuration
        if HAVE_AIOHTTP: # Only if aiohttp is available
            # Connector setup for proxies, if needed
            connector = None
            if use_proxy and proxy_url:
                if proxy_type == 'socks5' and HAVE_SOCKS:
                    try:
                        from aiohttp_socks import ProxyConnector
                        connector = ProxyConnector.from_url(proxy_url, ssl=ssl_verify)
                        self.logger.info(f"Using SOCKS5 proxy for aiohttp: {proxy_url}")
                    except ImportError:
                        self.logger.error("SOCKS proxy configured, but 'aiohttp_socks' is not installed. Please install it to use SOCKS proxies with async requests.")
                        # Fallback: self.session will be None or basic TCPConnector, SOCKS proxy won't be used by aiohttp
                elif proxy_type in ['http', 'https']:
                    # aiohttp uses http_proxy/https_proxy environment variables if trust_env=True
                    # No explicit connector needed for basic HTTP/HTTPS proxies via env vars.
                    self.logger.info(f"Using HTTP/HTTPS proxy (via environment variables) for aiohttp: {proxy_url}")
                    # os.environ['HTTP_PROXY'] = proxy_url # Ensure these are set if not relying on system-wide
                    # os.environ['HTTPS_PROXY'] = proxy_url


            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={'User-Agent': USER_AGENT},
                connector=connector, # Assign connector if created (e.g. for SOCKS)
                trust_env=True  # Allow environment variables for proxy settings
            )
            
            # Configure proxy if enabled
            if use_proxy and proxy_url:
                # For aiohttp, proxy is typically configured via environment variables (trust_env=True)
                # or by passing a proxy URL to the request methods directly.
                # For a session-wide proxy, especially SOCKS, a custom connector is needed.
                # The existing logic for SOCKS seems incomplete as aiohttp needs aiohttp-socks.
                # We will rely on trust_env=True for HTTP/HTTPS proxies for now.
                # If SOCKS is specified and aiohttp-socks is not integrated, it won't work as expected.
                if proxy_type == 'socks5' and HAVE_SOCKS:
                    # This part would require aiohttp-socks or similar
                    self.logger.warning("SOCKS proxy configured, but aiohttp needs aiohttp-socks for full SOCKS support within the session.")
                    # Example (if aiohttp-socks was integrated):
                    # from aiohttp_socks import ProxyConnector
                    # connector = ProxyConnector.from_url(proxy_url, ssl=ssl_verify)
                    # self.session = aiohttp.ClientSession(connector=connector, ...)
                    pass # Keep current connector, user might set env vars

                # For HTTP/HTTPS, aiohttp respects http_proxy, https_proxy env vars with trust_env=True
                # No need to explicitly set connector for basic HTTP/HTTPS proxy via env vars.
                # The os.environ calls are redundant if trust_env=True is used and could be removed
                # if we want to rely solely on aiohttp's env var handling.
                # However, to be explicit or if trust_env has different behavior:
                # os.environ['http_proxy'] = proxy_url # These might better be set outside, or handled by trust_env
                # os.environ['https_proxy'] = proxy_url

        else:
            self.logger.warning("aiohttp is not installed. Asynchronous features like registry checks will be unavailable.")
            self.session = None # Explicitly set to None if aiohttp is not available

    async def check_repository(self, repo_full_name: str, changed_files: List[str], platform: str, ref: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Check a repository for dependency confusion issues.
        
        Args:
            repo_full_name: Full name of the repository (org/repo)
            changed_files: List of changed package files to check
            platform: The platform of the repository ('github' or 'gitlab')
            ref: Optional commit SHA, branch name, or tag to get files from.
            
        Returns:
            List of findings
        """
        if not self.enabled:
            self.logger.info(f"Dependency confusion check disabled. Skipping {repo_full_name}.")
            return []

        if not self.session and HAVE_AIOHTTP : # If session is None even if aiohttp is available, means init failed
             await self._init_http_session() # Try to initialize session if not already
             if not self.session:
                self.logger.error(f"Failed to initialize HTTP session for {repo_full_name}. Cannot perform checks.")
                return []
        elif not HAVE_AIOHTTP:
            self.logger.warning(f"aiohttp not available, cannot perform registry checks for {repo_full_name}.")
            # Depending on policy, could return here or allow parsing to happen without checks.
            # For now, let's allow parsing but checks will fail/be skipped.

        findings = []

        files_with_content = []
        for file_path in changed_files:
            self.logger.debug(f"Fetching content for {file_path} in {repo_full_name} on {platform}")
            # self.repo_identifier.get_file_content is synchronous
            file_content = await asyncio.to_thread(
                self.repo_identifier.get_file_content,
                repo_full_name,
                file_path,
                platform,
                ref=ref
            )
            if file_content:
                files_with_content.append({'path': file_path, 'content': file_content})
            else:
                self.logger.warning(f"Could not fetch content for {file_path} in {repo_full_name}. Skipping.")

        for item in files_with_content:
            try:
                # _get_package_data is now synchronous and returns a list of deps
                parsed_deps = self._get_package_data(
                    file_path=item['path'],
                    file_content=item['content'],
                    repo_full_name=repo_full_name
                )

                if not parsed_deps:
                    self.logger.info(f"No dependencies found or parsed in {item['path']} for {repo_full_name}.")
                    continue

                # _check_dependency_confusion should now iterate through parsed_deps
                # and perform checks for each.
                # Assuming _check_dependency_confusion is adapted to take a list of deps
                # or we call it per dependency.
                # For simplicity, let's assume it processes a list of deps
                # and returns a list of findings.
                # If it processes one by one, we'd loop here.

                # Let's refine: _check_dependency_confusion should check ONE package_data dict
                # So we iterate here.
                for dep_data in parsed_deps:
                    # Ensure dep_data is not None and is a dictionary
                    if dep_data and isinstance(dep_data, dict):
                        finding = await self._check_dependency_confusion(dep_data)
                        if finding:
                            findings.append(finding)
                    else:
                        self.logger.warning(f"Invalid dependency data skipped for {item['path']}: {dep_data}")

            except Exception as e:
                self.logger.error(f"Error processing file {item['path']} in {repo_full_name}: {e}", exc_info=True)
                continue
                
        return findings

    def _get_package_data(self, file_path: str, file_content: str, repo_full_name: str) -> List[Dict[str, Any]]:
        """
        Parses a given file content to extract dependencies. Synchronous.
        """
        deps = []
        filename_lower = os.path.basename(file_path.lower()) # Use os.path.basename
        ecosystem = None

        # Determine ecosystem - more robustly
        for pattern, eco in self.supported_files.items():
            if fnmatch.fnmatch(filename_lower, pattern.lower()): # fnmatch for wildcards like *.csproj
                ecosystem = eco
                break
        
        if not ecosystem: # Double check for common patterns if not found by exact match
            if filename_lower.startswith("requirements") and filename_lower.endswith((".txt", ".in")):
                ecosystem = "pip"
            elif filename_lower == "package.json": # Common case
                ecosystem = "npm"
            # Add other specific checks if fnmatch isn't catching them.
            # Example for .csproj which should be caught by fnmatch if pattern is "*.csproj"
            elif filename_lower.endswith(".csproj"):
                 ecosystem = "nuget"


        if not ecosystem:
            self.logger.warning(f"Unsupported file type or ecosystem for {file_path} in {repo_full_name}. Filename: {filename_lower}")
            return []

        self.logger.info(f"Parsing {file_path} (ecosystem: {ecosystem}) in {repo_full_name}")

        try:
            if ecosystem == "pip":
                if filename_lower == "pyproject.toml":
                    deps = self._parse_pyproject_toml(file_content, file_path)
                elif filename_lower in ("pipfile", "pipfile.lock"): # Assuming Pipfile is TOML
                    deps = self._parse_pipfile(file_content, file_path)
                else: # requirements.txt, requirements.in, etc.
                    deps = self._parse_requirements_txt(file_content, file_path)
            elif ecosystem == "npm":
                deps = self._parse_package_json(file_content, file_path)
            elif ecosystem == "rubygems":
                deps = self._parse_gemfile(file_content, file_path)
            elif ecosystem == "maven":
                deps = self._parse_pom_xml(file_content, file_path)
            elif ecosystem == "composer":
                deps = self._parse_composer_json(file_content, file_path)
            elif ecosystem == "gomodules":
                deps = self._parse_go_mod(file_content, file_path)
            elif ecosystem == "nuget": # Handles both packages.config and *.csproj
                deps = self._parse_csproj_or_packages_config(file_content, file_path, ecosystem)
            else:
                self.logger.warning(f"No parser implemented for ecosystem '{ecosystem}' for file {file_path}")

        except Exception as e:
            self.logger.error(f"Failed to parse {file_path} for ecosystem {ecosystem}: {e}", exc_info=True)
            return [] # Return empty list on parsing failure for this file
            
        return deps

    # --- Parser Helper Methods ---
    def _parse_requirements_txt(self, content: str, file_path: str) -> List[Dict]:
        dependencies = []
        for line in content.splitlines():
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            # Basic parsing: name[operator]version. Does not handle complex cases like hashes, URLs, editable installs yet.
            match = re.match(r"^\s*([a-zA-Z0-9._-]+)\s*([<>=!~^]*)\s*([a-zA-Z0-9._*+-]*)\s*", line)
            if match:
                name = normalize_pypi_package_name(match.group(1))
                version_spec = f"{match.group(2)}{match.group(3)}".strip() if (match.group(2) or match.group(3)) else None
                dependencies.append({'name': name, 'version': version_spec, 'ecosystem': 'pip', 'source_file': file_path, 'scope': None})
            elif re.match(r"^[a-zA-Z0-9._-]+$", line): # Just a name
                 name = normalize_pypi_package_name(line)
                 dependencies.append({'name': name, 'version': None, 'ecosystem': 'pip', 'source_file': file_path, 'scope': None})
            else:
                self.logger.warning(f"Could not parse line in {file_path}: '{line}'")
        return dependencies

    def _parse_pipfile(self, content: str, file_path: str) -> List[Dict]:
        if not HAVE_TOML_PARSER:
            self.logger.warning(f"TOML parser not available, skipping Pipfile: {file_path}")
            return []
        dependencies = []
        try:
            data = tomllib.loads(content)
            for section, scope in [('packages', None), ('dev-packages', 'dev')]:
                if section in data:
                    for name, version_spec in data[section].items():
                        norm_name = normalize_pypi_package_name(name)
                        dependencies.append({'name': norm_name, 'version': str(version_spec) if version_spec != "*" else None, 'ecosystem': 'pip', 'source_file': file_path, 'scope': scope})
        except tomllib.TOMLDecodeError as e:
            self.logger.error(f"Error decoding TOML in {file_path}: {e}")
        return dependencies

    def _parse_pyproject_toml(self, content: str, file_path: str) -> List[Dict]:
        if not HAVE_TOML_PARSER:
            self.logger.warning(f"TOML parser not available, skipping pyproject.toml: {file_path}")
            return []
        dependencies = []
        try:
            data = tomllib.loads(content)

            # Poetry
            if 'tool' in data and 'poetry' in data['tool']:
                poetry_data = data['tool']['poetry']
                # Main dependencies
                if 'dependencies' in poetry_data:
                    for name, version_spec in poetry_data['dependencies'].items():
                        if name.lower() == 'python': continue # Skip python version spec
                        norm_name = normalize_pypi_package_name(name)
                        dependencies.append({'name': norm_name, 'version': str(version_spec) if isinstance(version_spec, str) else None, 'ecosystem': 'pip', 'source_file': file_path, 'scope': None})
                # Dev dependencies
                for group_name in ['dev-dependencies', 'group.dev.dependencies']: # Check both common forms
                    path = group_name.split('.')
                    current_section = poetry_data
                    try:
                        for p_item in path: current_section = current_section[p_item]
                        if isinstance(current_section, dict):
                             for name, version_spec in current_section.items():
                                norm_name = normalize_pypi_package_name(name)
                                dependencies.append({'name': norm_name, 'version': str(version_spec) if isinstance(version_spec, str) else None, 'ecosystem': 'pip', 'source_file': file_path, 'scope': 'dev'})
                    except KeyError:
                        pass # Section not found

            # PEP 621 (standard)
            if 'project' in data:
                project_data = data['project']
                if 'dependencies' in project_data: # Array of strings
                    for dep_str in project_data['dependencies']:
                        match = re.match(r"([a-zA-Z0-9._-]+)\s*([<>=!~^\[\]\s\w.,'-]*)", dep_str) # More complex parsing might be needed for extras
                        if match:
                            norm_name = normalize_pypi_package_name(match.group(1))
                            # Version spec might include extras, e.g., "requests[security]==2.25.1"
                            # For now, we take the whole string as version for simplicity, or None
                            version_part = match.group(2).strip()
                            dependencies.append({'name': norm_name, 'version': version_part if version_part else None, 'ecosystem': 'pip', 'source_file': file_path, 'scope': None})
                if 'optional-dependencies' in project_data: # Dict of arrays
                    for group_name, dep_list in project_data['optional-dependencies'].items():
                        for dep_str in dep_list:
                            match = re.match(r"([a-zA-Z0-9._-]+)\s*([<>=!~^\[\]\s\w.,'-]*)", dep_str)
                            if match:
                                norm_name = normalize_pypi_package_name(match.group(1))
                                version_part = match.group(2).strip()
                                dependencies.append({'name': norm_name, 'version': version_part if version_part else None, 'ecosystem': 'pip', 'source_file': file_path, 'scope': group_name})
        except tomllib.TOMLDecodeError as e:
            self.logger.error(f"Error decoding TOML in {file_path}: {e}")
        return dependencies

    def _parse_package_json(self, content: str, file_path: str) -> List[Dict]:
        dependencies = []
        try:
            data = json.loads(content)
            for section, scope in [('dependencies', None), ('devDependencies', 'dev'), ('peerDependencies', 'peer')]:
                if section in data:
                    for name, version_spec in data[section].items():
                        # npm package names are case-sensitive but typically lowercase. No aggressive normalization needed here.
                        dependencies.append({'name': name, 'version': str(version_spec), 'ecosystem': 'npm', 'source_file': file_path, 'scope': scope})
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON in {file_path}: {e}")
        return dependencies

    def _parse_gemfile(self, content: str, file_path: str) -> List[Dict]:
        dependencies = []
        # Regex for: gem 'name', 'version', group: :dev or groups: [:dev, :test]
        # This is a simplified regex and might not cover all Gemfile syntax variations.
        gem_pattern = re.compile(r"^\s*gem\s+['\"]([^'\"]+)['\"](?:,\s*['\"]([^'\"]+)['\"])?(.*)", re.MULTILINE)
        group_pattern = re.compile(r"group(?:s)?\s*:\s*([a-zA-Z0-9_,:\s]+)")

        current_groups = None # For handling group blocks
        group_block_pattern = re.compile(r"^\s*group\s*((?::\w+\s*,?\s*)+)\s*(?:do|\s)")
        end_pattern = re.compile(r"^\s*end\s*$")

        for line in content.splitlines():
            line = line.strip()
            if not line or line.startswith('#'):
                continue

            # Check for group blocks
            group_match = group_block_pattern.match(line)
            if group_match:
                # Extract group names (e.g. :development, :test -> ['development', 'test'])
                current_groups = [g.strip().lstrip(':') for g in group_match.group(1).split(',')]
                continue

            if end_pattern.match(line): # End of a group block
                current_groups = None
                continue

            match = gem_pattern.match(line)
            if match:
                name = match.group(1)
                version_spec = match.group(2) if match.group(2) else None
                options_str = match.group(3)

                scope = None
                if current_groups: # Inside a group block
                    scope = ", ".join(current_groups)
                else: # Check for inline group option
                    inline_group_match = group_pattern.search(options_str)
                    if inline_group_match:
                        scope_str = inline_group_match.group(1).replace(':', '').replace('[', '').replace(']', '').strip()
                        scope = ", ".join([s.strip() for s in scope_str.split(',')])


                dependencies.append({'name': normalize_rubygems_package_name(name), 'version': version_spec, 'ecosystem': 'rubygems', 'source_file': file_path, 'scope': scope})
        return dependencies

    def _parse_pom_xml(self, content: str, file_path: str) -> List[Dict]:
        dependencies = []
        try:
            root = ET.fromstring(content)
            # Namespace handling: POMs can have a default namespace
            ns_match = re.match(r'{.*}', root.tag)
            ns = {'m': ns_match.group(0)[1:-1]} if ns_match else {'m': 'http://maven.apache.org/POM/4.0.0'} # Default if no ns in root

            # Properties for version substitution
            properties = {}
            props_node = root.find('m:properties', ns)
            if props_node is not None:
                for prop in props_node:
                    tag_name = prop.tag.split('}')[-1] # remove namespace prefix
                    properties[f"${{{tag_name}}}"] = prop.text

            for dep_node in root.findall('.//m:dependency', ns):
                groupId = dep_node.find('m:groupId', ns)
                artifactId = dep_node.find('m:artifactId', ns)
                version_node = dep_node.find('m:version', ns)
                scope_node = dep_node.find('m:scope', ns)

                if groupId is not None and artifactId is not None:
                    name = f"{groupId.text}:{artifactId.text}"
                    version_str = version_node.text if version_node is not None else None
                    # Substitute properties in version
                    if version_str and version_str in properties:
                        version_str = properties[version_str]

                    scope = scope_node.text if scope_node is not None else None
                    dependencies.append({'name': name, 'version': version_str, 'ecosystem': 'maven', 'source_file': file_path, 'scope': scope})
        except ET.ParseError as e:
            self.logger.error(f"Error parsing XML in {file_path}: {e}")
        return dependencies

    def _parse_composer_json(self, content: str, file_path: str) -> List[Dict]:
        dependencies = []
        try:
            data = json.loads(content)
            for section, scope in [('require', None), ('require-dev', 'dev')]:
                if section in data:
                    for name, version_spec in data[section].items():
                        if name.lower() in ["php", "composer-plugin-api", "composer-runtime-api"] or name.startswith("ext-"): # Skip platform packages
                            continue
                        dependencies.append({'name': normalize_composer_package_name(name), 'version': str(version_spec), 'ecosystem': 'composer', 'source_file': file_path, 'scope': scope})
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON in {file_path}: {e}")
        return dependencies

    def _parse_go_mod(self, content: str, file_path: str) -> List[Dict]:
        dependencies = []
        # Regex for `require module/path v1.2.3` or `require module/path v0.0.0-timestamp-commit`
        # Also handles `require (` blocks.
        # Does not handle `replace` or `exclude` directives for now.
        require_pattern = re.compile(r"^\s*(?:require|replace)\s+(?:\(([\s\S]*?)\)|([\w./-]+)\s+([\w./+-]+))\s*(?://\s*(indirect))?", re.MULTILINE)
        line_pattern = re.compile(r"^\s*([\w./-]+)\s+([\w./+-]+)\s*(?://\s*(indirect))?")

        for match in require_pattern.finditer(content):
            block_content, single_module, single_version, single_indirect = match.groups()

            items_to_parse = []
            if block_content:
                for line in block_content.strip().splitlines():
                    line = line.strip()
                    if not line or line.startswith("//"): continue
                    m = line_pattern.match(line)
                    if m: items_to_parse.append(m.groups())
            elif single_module and single_version:
                items_to_parse.append((single_module, single_version, single_indirect))

            for module_path, version_spec, indirect_comment in items_to_parse:
                scope = "indirect" if indirect_comment else None
                # Go module paths are case-sensitive. No normalization here.
                dependencies.append({'name': module_path, 'version': version_spec, 'ecosystem': 'gomodules', 'source_file': file_path, 'scope': scope})
        return dependencies

    def _parse_csproj_or_packages_config(self, content: str, file_path: str, ecosystem: str) -> List[Dict]:
        dependencies = []
        filename_lower = os.path.basename(file_path.lower())
        try:
            root = ET.fromstring(content)
            if filename_lower == "packages.config":
                for package_node in root.findall('.//package'):
                    pkg_id = package_node.get('id')
                    pkg_version = package_node.get('version')
                    dev_dep = package_node.get('developmentDependency', 'false').lower() == 'true'
                    scope = 'dev' if dev_dep else None
                    if pkg_id:
                        dependencies.append({'name': normalize_nuget_package_id(pkg_id), 'version': pkg_version, 'ecosystem': ecosystem, 'source_file': file_path, 'scope': scope})
            elif filename_lower.endswith(".csproj"):
                 # .csproj files can have different structures depending on SDK style or classic.
                 # Common is PackageReference under an ItemGroup.
                for item_group in root.findall('.//ItemGroup'): # Path might need namespace handling if present
                    for ref_node in item_group.findall('.//PackageReference'):
                        pkg_id = ref_node.get('Include')
                        pkg_version = ref_node.get('Version') # Direct attribute
                        if not pkg_version: # Sometimes version is a child element
                            version_elem = ref_node.find('.//Version')
                            if version_elem is not None: pkg_version = version_elem.text

                        if pkg_id:
                            dependencies.append({'name': normalize_nuget_package_id(pkg_id), 'version': pkg_version, 'ecosystem': ecosystem, 'source_file': file_path, 'scope': None}) # Scope not typically in PackageReference
        except ET.ParseError as e:
            self.logger.error(f"Error parsing XML in {file_path}: {e}")
        return dependencies

    async def _check_dependency_confusion(self, package_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Checks a single package for dependency confusion and typosquatting vulnerabilities.
        """
        package_name = package_data.get('name')
        ecosystem = package_data.get('ecosystem')
        version = package_data.get('version') # May be None or a specifier
        source_file = package_data.get('source_file')
        scope = package_data.get('scope') # e.g., 'dev', 'peer'

        if not package_name or not ecosystem:
            self.logger.warning(f"Missing name or ecosystem in package_data: {package_data} from {source_file}")
            return None

        self.logger.debug(f"Checking {ecosystem} package: '{package_name}' (version: {version or 'any'}) from {source_file}")

        # 1. Normalize Package Name for Checking
        normalized_name_for_check = package_name
        if ecosystem == 'pip':
            normalized_name_for_check = normalize_pypi_package_name(package_name)
        elif ecosystem == 'npm':
            # For scoped npm packages like @scope/pkg, the normalized name for registry check is usually the full name.
            # The part for typosquatting check will be handled later.
            normalized_name_for_check = package_name
        elif ecosystem == 'maven':
            # Maven 'name' is already 'groupId:artifactId'. Normalization for lookup isn't typically needed here
            # if parsers provide it in the correct format.
            normalized_name_for_check = package_name
        elif ecosystem == 'composer':
            normalized_name_for_check = normalize_composer_package_name(package_name)
        elif ecosystem == 'rubygems':
            normalized_name_for_check = normalize_rubygems_package_name(package_name)
        elif ecosystem == 'nuget':
            normalized_name_for_check = normalize_nuget_package_id(package_name)
        # gomodules paths are case-sensitive in parts, handle with care or assume direct usage.

        # 2. Internal Package Identification
        is_internal_by_pattern = False
        if self.internal_package_regex:
            # For Maven, check against artifactId or groupId:artifactId
            check_target_for_pattern = package_name
            if ecosystem == 'maven':
                check_target_for_pattern = package_name.split(':')[-1] # artifactId

            if self.internal_package_regex.match(check_target_for_pattern):
                is_internal_by_pattern = True
                self.logger.debug(f"Package '{package_name}' matches internal pattern: '{self.internal_package_regex.pattern}'")

        is_internal_by_namespace = False
        if self.secure_namespaces:
            if ecosystem == 'npm' and package_name.startswith('@'):
                scope_name = package_name.split('/')[0]
                if scope_name in self.secure_namespaces:
                    is_internal_by_namespace = True
            elif ecosystem == 'maven':
                groupId = package_name.split(':')[0]
                if groupId in self.secure_namespaces:
                    is_internal_by_namespace = True
                else: # Check for partial matches like com.mycompany from com.mycompany.product
                    for ns in self.secure_namespaces:
                        if groupId.startswith(ns + "."):
                            is_internal_by_namespace = True
                            break
            elif ecosystem == 'pip': # Example for pip if using namespace packages like 'mycompany.product'
                 # This requires a clear definition of what constitutes a "namespace" for pip in config.
                 # For now, let's assume simple prefix matching if applicable.
                for ns in self.secure_namespaces:
                    if package_name.startswith(ns + "."): # or ns == package_name.split('.')[0]
                        is_internal_by_namespace = True
                        break
            # Other ecosystems might need specific namespace logic.

        if is_internal_by_namespace:
             self.logger.debug(f"Package '{package_name}' identified as internal by secure namespace.")

        is_considered_internal = is_internal_by_pattern or is_internal_by_namespace

        # 3. Public Registry Check (Dependency Confusion)
        registry_check_method_name = f"_check_registry_{ecosystem.lower()}"
        public_package_info = None
        if hasattr(self, registry_check_method_name) and callable(getattr(self, registry_check_method_name)):
            registry_check_method = getattr(self, registry_check_method_name)
            public_package_info = await registry_check_method(normalized_name_for_check)
        else:
            self.logger.warning(f"No registry check method found for ecosystem: {ecosystem} (package: {package_name})")

        if public_package_info:
            self.logger.info(f"Package '{normalized_name_for_check}' exists in public {ecosystem} registry.")
            if is_considered_internal:
                finding = {
                    'type': 'dependency_confusion',
                    'package_name': package_name,
                    'version': version,
                    'ecosystem': ecosystem,
                    'source_file': source_file,
                    'scope': scope,
                    'public_name_checked': normalized_name_for_check,
                    'details': f"Internal package '{package_name}' (checked as '{normalized_name_for_check}') "
                               f"found in public {ecosystem} registry.",
                    'public_package_data': public_package_info.get('data')
                }
                self.logger.warning(f"{finding['details']} (File: {source_file})")
                return finding # Dependency confusion finding takes precedence
        elif is_considered_internal:
            self.logger.debug(f"Internal package '{package_name}' (checked as '{normalized_name_for_check}') "
                             f"not found in public {ecosystem} registry. Safe from direct confusion.")


        # 4. Typosquatting Check (only if not an internal package that was found publicly)
        if not self.enable_typosquatting_detection:
            self.logger.debug(f"Typosquatting detection disabled. Skipping for '{package_name}'.")
            return None

        # Determine the part of the name to check for typosquatting
        name_to_check_typo = package_name
        if ecosystem == 'pip':
            name_to_check_typo = normalized_name_for_check # Already normalized for pip
        elif ecosystem == 'npm':
            name_to_check_typo = package_name.split('/')[-1]
        elif ecosystem == 'maven':
            name_to_check_typo = package_name.split(':')[-1] # artifactId
        elif ecosystem == 'composer':
            name_to_check_typo = package_name.split('/')[-1] if '/' in package_name else package_name
        # For others, normalized_name_for_check or package_name might be okay.

        relevant_top_packages = self.top_packages.get(ecosystem, [])
        if not relevant_top_packages:
            self.logger.debug(f"No top packages list for ecosystem '{ecosystem}'. Skipping typosquatting check for '{package_name}'.")
            return None

        for top_package_name in relevant_top_packages:
            # Normalize top_package_name similar to how normalized_name_for_check was created
            normalized_top_package_name = top_package_name
            if ecosystem == 'pip':
                normalized_top_package_name = normalize_pypi_package_name(top_package_name)
            elif ecosystem == 'npm': # Assuming top_packages for npm are just the package part, not @scope/pkg
                 pass # Direct comparison
            elif ecosystem == 'maven': # Assuming top_packages for maven are artifactIds
                 pass # Direct comparison
            elif ecosystem == 'composer': # Assuming top packages are 'package' part of 'vendor/package'
                 normalized_top_package_name = normalize_composer_package_name(top_package_name).split('/')[-1]


            if not name_to_check_typo or not normalized_top_package_name: continue

            distance = calculate_levenshtein_distance(name_to_check_typo, normalized_top_package_name)

            # Adjust threshold based on the length of the package name being checked
            # Longer names can have a slightly higher absolute distance and still be a typo
            threshold = self.typosquatting_threshold + (len(name_to_check_typo) // self.typosquatting_length_divisor)

            if 0 < distance <= threshold and name_to_check_typo != normalized_top_package_name:
                # If the potentially typosquatted package itself exists publicly, make that clear.
                # This check is primarily about the *name similarity*, not existence of the typo candidate itself.
                # However, if the typo candidate *is* the one that exists publicly (public_package_info relates to name_to_check_typo),
                # that's more of a direct risk.

                details = (f"Package '{package_name}' (name part: '{name_to_check_typo}') is potentially typosquatting "
                           f"on popular {ecosystem} package '{normalized_top_package_name}' "
                           f"(Levenshtein distance: {distance}, threshold: {threshold}).")

                # If the package that triggered the typo check (name_to_check_typo) was found publicly,
                # this is a stronger signal.
                if public_package_info and normalized_name_for_check == name_to_check_typo : # Check if the public package is the one we are checking for typo
                     details += " This package itself was found in the public registry."

                finding = {
                    'type': 'typosquatting',
                    'package_name': package_name,
                    'version': version,
                    'ecosystem': ecosystem,
                    'source_file': source_file,
                    'scope': scope,
                    'suspected_typo_of': normalized_top_package_name,
                    'name_checked_for_typo': name_to_check_typo,
                    'levenshtein_distance': distance,
                    'similarity_threshold': threshold,
                    'details': details,
                    'public_package_data': public_package_info.get('data') if public_package_info and normalized_name_for_check == name_to_check_typo else None
                }
                self.logger.warning(f"{finding['details']} (File: {source_file})")
                return finding

        return None # No vulnerabilities found
            
    async def close(self) -> None:
        """Clean up any open resources."""
        if isinstance(self.session, aiohttp.ClientSession): # Only close if it's an aiohttp session
            await self.session.close()
        # No need to explicitly close requests.Session in this manner

    async def _generic_registry_check(self, package_name: str, registry_url: str, package_type: str) -> Optional[Dict[str, Any]]:
        """Generic asynchronous registry check using aiohttp.
        
        Args:
            package_name: Name of the package to check
            registry_url: URL of the registry to check
            package_type: Type of package (e.g., 'pypi', 'npm')
            
        Returns:
            Dict with package info if found, None otherwise
        """
        try:
            # Format the URL with the package name
            # For Maven, package_name is "group:artifact"
            if package_type == "maven":
                group, artifact = package_name.split(":")
                url = registry_url.format(group=group.replace('.', '/'), artifact=artifact)
            elif package_type == "gomodules":
                # Go module paths need special encoding for uppercase letters for the proxy
                encoded_package_name = urllib.parse.quote(package_name.replace('!', '!').replace('A','!a').replace('B','!b').replace('C','!c').replace('D','!d').replace('E','!e').replace('F','!f').replace('G','!g').replace('H','!h').replace('I','!i').replace('J','!j').replace('K','!k').replace('L','!l').replace('M','!m').replace('N','!n').replace('O','!o').replace('P','!p').replace('Q','!q').replace('R','!r').replace('S','!s').replace('T','!t').replace('U','!u').replace('V','!v').replace('W','!w').replace('X','!x').replace('Y','!y').replace('Z','!z'))
                url = registry_url.format(package=encoded_package_name)
            else:
                url = registry_url.format(package=urllib.parse.quote(package_name))


            # Make the request
            if not isinstance(self.session, aiohttp.ClientSession):
                self.logger.error(f"Attempted async registry check for {package_name} without an aiohttp session. HAVE_AIOHTTP: {HAVE_AIOHTTP}")
                return None # Cannot proceed without an aiohttp session

            try:
                async with self.session.get(url) as response: # Session timeout applies
                    response.raise_for_status()
                    if package_type == "maven":
                        # Maven returns XML. For existence check, 200 OK is sufficient.
                        # Further parsing could be added if specific data from XML is needed.
                        # Example: raw_xml = await response.text(); tree = ET.fromstring(raw_xml)
                        data = {"status": "exists_xml_confirmed"}
                    elif package_type == "gomodules":
                        # Go proxy returns a list of versions (text), not JSON.
                        # A 200 OK means the module path exists.
                        # Example: version_list = await response.text()
                        data = {"status": "exists_text_list_confirmed"}
                    else:
                        # Most registries return JSON
                        data = await response.json()
            
            except ClientResponseError as e: # Handles HTTP status errors (4xx, 5xx)
                if e.status == 404: # Package not found is a valid outcome, not an error for this check.
                    return None
                self.logger.error(f"HTTP error checking {package_type} registry for {package_name} at {url}: {e.status} {e.message}")
                return None
            except ClientError as e: # Handles other client-side errors (connection issues, timeouts handled by session, etc.)
                self.logger.error(f"Client error checking {package_type} registry for {package_name} at {url}: {e}")
                return None
            except Exception as e: # Catch-all for other unexpected errors (e.g., JSON parsing if not valid JSON)
                self.logger.error(f"Unexpected error processing {package_type} registry for {package_name} at {url}: {e}")
                return None
            
            # Return the package info
            return {
                'name': package_name,
                'type': package_type,
                'registry': registry_url,
                'data': data
            }
            
        except Exception as e: # Catch errors in URL formatting or other pre-request logic
            self.logger.error(f"Unexpected error preparing registry check for {package_type} {package_name}: {e}")
            return None

    async def _check_registry_pypi(self, package_name: str) -> Optional[Dict[str, Any]]:
        """Check if a package exists on PyPI."""
        return await self._generic_registry_check(
            package_name=package_name,
            registry_url=self.PYPI_REGISTRY_URL,
            package_type='pypi'
        )

    async def _check_registry_npm(self, package_name: str) -> Optional[Dict[str, Any]]:
        """Check if a package exists on npm."""
        return await self._generic_registry_check(
            package_name=package_name,
            registry_url=self.NPM_REGISTRY_URL,
            package_type='npm'
        )

    async def _check_registry_rubygems(self, package_name: str) -> Optional[Dict[str, Any]]:
        """Check if a package exists on RubyGems."""
        return await self._generic_registry_check(
            package_name=package_name,
            registry_url=self.RUBYGEMS_REGISTRY_URL,
            package_type='rubygems'
        )

    async def _check_registry_composer(self, package_name: str) -> Optional[Dict[str, Any]]:
        """Check if a package exists on Packagist."""
        return await self._generic_registry_check(
            package_name=package_name,
            registry_url=self.COMPOSER_REGISTRY_URL,
            package_type='composer'
        )

    async def _check_registry_gomodules(self, package_name: str) -> Optional[Dict[str, Any]]:
        """Check if a package exists on Go Modules."""
        return await self._generic_registry_check(
            package_name=package_name,
            registry_url=self.GOMODULES_REGISTRY_URL,
            package_type='gomodules'
        )

    async def _check_registry_nuget(self, package_name: str) -> Optional[Dict[str, Any]]:
        """Check if a package exists on NuGet."""
        return await self._generic_registry_check(
            package_name=package_name,
            registry_url=self.NUGET_REGISTRY_URL,
            package_type='nuget'
        )
