[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "depconf-checker"
version = "0.3.0"
description = "A tool for detecting dependency confusion vulnerabilities in package managers"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
requires-python = ">=3.8"
# Core dependencies
dependencies = [
  "click >= 8.0",
  "pyyaml >= 6.0",
  "colorama >= 0.4",
  "requests >= 2.25", # For notifications, registry checks & repo identifier
  # --- Git platform libraries ---
  "PyGithub >= 1.55", # For GitHub API interaction
  # --- Parsing dependencies ---
  "tomli; python_version < '3.11'", # For pyproject.toml parsing on Python < 3.11
]

[project.scripts]
depconf = "depconf.cli:cli" # Define the entry point

[project.optional-dependencies]
gitlab = ["python-gitlab >= 3.0"] # Optional GitLab support
async = ["aiohttp >= 3.8", "aiohttp-socks >= 0.7"]  # For async HTTP checks with SOCKS support
levenshtein = ["python-Levenshtein >= 0.12"]  # For faster typosquatting checks
jellyfish = ["jellyfish >= 0.9"]  # For alternative/advanced typosquatting checks
socks = ["PySocks >= 1.7"]  # For SOCKS proxy support with requests
dev = [  # Development dependencies
    "pytest >= 6.0",
    "pytest-cov >= 2.0",
    "pytest-asyncio >= 0.18",
    "black >= 21.0",
    "flake8 >= 3.8",
    "mypy >= 0.800",
    "pre-commit >= 2.0",
    "bandit >= 1.7",  # Security linting
    "safety >= 1.10"  # Dependency vulnerability scanning
]
test = [  # Test-only dependencies
    "pytest >= 6.0",
    "pytest-cov >= 2.0",
    "pytest-asyncio >= 0.18",
    "coverage >= 5.0"
]
all = [  # Convenience group to install all optional features
    "depconf-checker[gitlab]",
    "depconf-checker[async]",
    "depconf-checker[levenshtein]",
    "depconf-checker[jellyfish]",
    "depconf-checker[socks]"
]

[tool.setuptools.packages.find]
where = ["."]
include = ["depconf*"]
exclude = ["tests*", "docs*"]

# Development tool configurations
[tool.black]
line-length = 100
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "security: marks tests as security-related tests"
]

[tool.coverage.run]
source = ["depconf"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__main__.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "github.*",
    "gitlab.*",
    "aiohttp.*",
    "requests.*",
    "yaml.*",
    "colorama.*",
    "click.*"
]
ignore_missing_imports = true

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101"]  # Skip assert_used test

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".tox"
]