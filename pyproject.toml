[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "depconf-checker"
version = "0.3.0"
description = "A tool for detecting dependency confusion vulnerabilities in package managers"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
requires-python = ">=3.8"
# Core dependencies
dependencies = [
  "click >= 8.0",
  "pyyaml >= 6.0",
  "colorama >= 0.4",
  "requests >= 2.25", # For notifications, registry checks & repo identifier
  # --- Git platform libraries ---
  "PyGithub >= 1.55", # For GitHub API interaction
  # --- Parsing dependencies ---
  "tomli; python_version < '3.11'", # For pyproject.toml parsing on Python < 3.11
]

[project.scripts]
depconf = "depconf.cli:cli" # Define the entry point

[project.optional-dependencies]
gitlab = ["python-gitlab >= 3.0"] # Optional GitLab support
async = ["aiohttp >= 3.8"]  # For async HTTP checks
levenshtein = ["python-Levenshtein >= 0.12"]  # For faster typosquatting checks
jellyfish = ["jellyfish >= 0.9"]  # For alternative/advanced typosquatting checks
socks = ["PySocks >= 1.7"]  # For SOCKS proxy support with requests
all = [  # Convenience group to install all optional features
    "depconf-checker[gitlab]",
    "depconf-checker[async]",
    "depconf-checker[levenshtein]",
    "depconf-checker[jellyfish]",
    "depconf-checker[socks]"
]

[tool.setuptools.packages.find]
where = ["."]
include = ["depconf*"]
exclude = ["tests*", "docs*"]